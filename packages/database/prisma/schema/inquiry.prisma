model Inquiry {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // セルフ・リレーション・バージョニングで実装しています
  // docs/detail-design/SELF_RELATION_VERSIONING.md
  /// 問い合わせ一覧を取得するときは originalId が null という条件が必要です
  originalId String?
  original   Inquiry?  @relation("versions", fields: [originalId], references: [id])
  versions   Inquiry[] @relation("versions")

  // 頻繁に更新される想定のフィールド
  status      InquiryStatus?
  urgencyId   String?
  urgency     InquiryUrgency?    @relation(fields: [urgencyId], references: [id])
  assignUsers InquiriesOnUsers[]
  assignTeams InquiriesOnTeams[]

  bicycleId        String?
  bicycle          Bicycle?        @relation(fields: [bicycleId], references: [id])
  receptionRouteId String?
  receptionRoute   ReceptionRoute? @relation(fields: [receptionRouteId], references: [id])
  receiverId       String?
  receiver         User?           @relation(fields: [receiverId], references: [id])

  comments      InquiryComment[]
  images        InquiryImage[]
  notifications Notification[]

  /// 年度(4月〜翌年3月)
  fiscalYear     Int?
  /// 年度(4月〜翌年3月)ごとの通し番号
  no             Int?
  /// 受付日時
  receivedAt     DateTime?
  /// 問い合わせタイトル
  title          String    @db.VarChar(100)
  /// 問い合わせ内容
  description    String    @db.VarChar(1000)
  /// 住所
  address         String?   @db.VarChar(100)
  /// 緯度
  lat            Float?
  /// 経度
  lng            Float?
  /// 備考
  memo           String?   @db.VarChar(1000)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([fiscalYear, no])
}

model InquiryImage {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  inquiryId String
  inquiry   Inquiry @relation(fields: [inquiryId], references: [id])

  /// 画像を格納したS3キー
  key         String
  /// 説明
  description String
  // 表示順
  sortOrder   Int    @default(0)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model InquiryComment {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  originalId        String?
  original          InquiryComment? @relation("comment_versions", fields: [originalId], references: [id])
  versions          InquiryComment[] @relation("comment_versions")

  // リレーション
  inquiryId String
  inquiry   Inquiry @relation(fields: [inquiryId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id])

  body   String                @db.VarChar(1000)
  images InquiryCommentImage[]

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model InquiryCommentImage {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  commentId String
  comment   InquiryComment @relation(fields: [commentId], references: [id])

  /// 画像を格納したS3キー
  key         String
  /// 説明
  description String
  // 表示順
  sortOrder   Int    @default(0)

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

/// 受付ルート
model ReceptionRoute {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  inquiries Inquiry[]

  /// 名称
  name      String
  /// 表示順
  sortOrder Int
  /// 備考
  memo      String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model InquiriesOnUsers {
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  inquiryId String
  inquiry   Inquiry @relation(fields: [inquiryId], references: [id])
  userId    String
  user      User    @relation(fields: [userId], references: [id])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([tenantId, inquiryId, userId])
}

model InquiriesOnTeams {
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  inquiryId String
  inquiry   Inquiry @relation(fields: [inquiryId], references: [id])
  teamId    String
  team      Team    @relation(fields: [teamId], references: [id])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([tenantId, inquiryId, teamId])
}

model InquiryUrgency {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // リレーション
  inquiries Inquiry[]

  /// 名称
  name      String
  /// カラーコード
  colorCode String  @db.VarChar(9) // 例:
  /// 表示順
  sortOrder Int
  /// 備考
  memo      String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

enum InquiryStatus {
  notRequiredAction
  notStarted
  inProgress
  waitingForReview
  completed
}
