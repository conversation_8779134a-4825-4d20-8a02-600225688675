import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import { notifyAssignment } from './assignment-utils';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: updaterId, tenantId } = ctx;
  const { id, receivedAt, assignUserId, assignmentChange } = input;

  await asyncTx(async (tx) => {
    const commonData = {
      title: input.title,
      description: input.description,
      address: input.address,
      memo: input.memo,
      receivedAt: input.receivedAt,
      receptionRoute: { connect: { id: input.receptionRouteId } },
      receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
    };

    const updateData: Prisma.InquiryUpdateInput = {
      ...commonData,
      fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
      images: { deleteMany: {}, createMany: { data: input.images } },
      assignUsers: assignUserId
        ? { deleteMany: {}, create: { userId: assignUserId } }
        : { deleteMany: {} },
    };

    const versionData: Prisma.InquiryCreateInput = {
      ...commonData,
      images: { createMany: { data: input.images } },
      assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
    };

    // Update the main inquiry
    await tx.inquiry.update({ data: updateData, where: { id } });

    // Create version
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Handle assignment notifications
    if (assignmentChange) {
      await notifyAssignment(tx, assignmentChange, id, input.title, updaterId, tenantId);
    }
  });

  return 'OK';
};

export const updateInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(updateInquiryMutation);
