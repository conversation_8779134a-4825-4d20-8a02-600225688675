-- CreateEnum
CREATE TYPE "BicycleMarkerStatus" AS ENUM ('todo', 'done');

-- CreateEnum
CREATE TYPE "BicycleStatus" AS ENUM ('find', 'ensureAbandoned', 'lose', 'ignore', 'remove', 'store', 'returnToOwner', 'sell', 'transfer', 'dispose', 'recycle', 'loseFromStorage');

-- CreateEnum
CREATE TYPE "BicycleEventType" AS ENUM ('find', 'ensureAbandoned', 'lose', 'ignore', 'remove', 'numbering', 'reNumbering', 'printSerialTag', 'store', 'assess', 'copyOwnerInfo', 'appendAnnouncementList', 'removeAnnouncementList', 'startAnnouncement', 'updateAnnouncement', 'cancelAnnouncement', 'requestPolice', 'receivePolice', 'cancelPolice', 'requestDltb', 'receiveDltb', 'cancelDltb', 'notify', 'updateNotification', 'cancelNotification', 'resetNotificationDate', 'extendDeadline', 'scheduleDeadline', 'updateDeadline', 'cancelDeadline', 'scheduleMoveBetweenStorage', 'moveBetweenStorage', 'returnToOwner', 'cancelReturnToOwner', 'collectStorageFee', 'cancelCollectStorageFee', 'sell', 'cancelSell', 'scheduleSell', 'editScheduleSell', 'cancelSellSchedule', 'transfer', 'cancelTransfer', 'scheduleTransfer', 'cancelTransferSchedule', 'dispose', 'cancelDispose', 'scheduleDispose', 'cancelDisposeSchedule', 'recycle', 'cancelRecycle', 'scheduleRecycle', 'cancelRecycleSchedule', 'loseFromStorage', 'cancelLoseFromStorage', 'create', 'editLocation', 'editBody', 'editOwner');

-- CreateEnum
CREATE TYPE "BicycleType" AS ENUM ('bicycle', 'motorizedBicycle', 'motorcycle', 'specifiedSmallMotorizedBicycle', 'other');

-- CreateEnum
CREATE TYPE "BicycleContractStatus" AS ENUM ('scheduled', 'done', 'canceled');

-- CreateEnum
CREATE TYPE "DealType" AS ENUM ('sell', 'transfer', 'dispose');

-- CreateEnum
CREATE TYPE "InquiryStatus" AS ENUM ('notRequiredAction', 'notStarted', 'inProgress', 'waitingForReview', 'completed');

-- CreateEnum
CREATE TYPE "PostalDataOwner" AS ENUM ('system', 'user');

-- CreateEnum
CREATE TYPE "Prefecture" AS ENUM ('北海道', '青森県', '岩手県', '宮城県', '秋田県', '山形県', '福島県', '茨城県', '栃木県', '群馬県', '埼玉県', '千葉県', '東京都', '神奈川県', '新潟県', '富山県', '石川県', '福井県', '山梨県', '長野県', '岐阜県', '静岡県', '愛知県', '三重県', '滋賀県', '京都府', '大阪府', '兵庫県', '奈良県', '和歌山県', '鳥取県', '島根県', '岡山県', '広島県', '山口県', '徳島県', '香川県', '愛媛県', '高知県', '福岡県', '佐賀県', '長崎県', '熊本県', '大分県', '宮崎県', '鹿児島県', '沖縄県');

-- CreateEnum
CREATE TYPE "ReferenceStatus" AS ENUM ('request', 'response', 'canceled');

-- CreateEnum
CREATE TYPE "ResponseStatus" AS ENUM ('success', 'notFound');

-- CreateEnum
CREATE TYPE "SourceType" AS ENUM ('body', 'policeReference', 'dltbReference', 'recipient', 'keepExtend', 'manual');

-- CreateEnum
CREATE TYPE "ReleaseType" AS ENUM ('dispose', 'recycle', 'sell', 'transfer');

-- CreateEnum
CREATE TYPE "DateFormatPattern" AS ENUM ('YYYY_MM_DD_SLASH', 'YY_MM_DD_SLASH', 'YYYY_MM_DD_HYPHEN', 'YY_MM_DD_HYPHEN');

-- CreateEnum
CREATE TYPE "SerialNoPartition" AS ENUM ('year', 'month', 'day', 'type', 'storage', 'landmark');

-- CreateEnum
CREATE TYPE "PatrolFlow" AS ENUM ('mark', 'find', 'noPatrol');

-- CreateEnum
CREATE TYPE "PoliceReferenceFileType" AS ENUM ('any', 'csv', 'xlsx');

-- CreateEnum
CREATE TYPE "AnnouncementFlow" AS ENUM ('each', 'monthly');

-- CreateEnum
CREATE TYPE "BodyField" AS ENUM ('colors', 'hasBasket', 'isLocked', 'conditions', 'style', 'free1', 'free2', 'registrationNumber', 'serialNumber', 'numberPlate');

-- CreateEnum
CREATE TYPE "SerialTagPaper" AS ENUM ('A4_vertical3x4', 'A4_vertical4x6');

-- CreateEnum
CREATE TYPE "SearchField" AS ENUM ('createdAt', 'removedAt', 'status', 'storage', 'storageLocationMemo', 'serialNo', 'bicycleType', 'registrationNumber', 'serialNumber', 'numberPlate', 'colors', 'hasBasket', 'isLocked', 'conditions', 'landmark', 'isNoParkingArea', 'address', 'isRemovedOrStored', 'referenceStatus', 'ownerName', 'ownerPostalCode', 'ownerAddress', 'theftReport', 'announceStatus', 'isReturned', 'isCollectedStorageFee', 'isReleased', 'releaseStatus', 'ownerStatus');

-- CreateEnum
CREATE TYPE "SearchItemType" AS ENUM ('text', 'switch', 'selectSingle', 'selectMultiple', 'date', 'dateAfter', 'dateBefore');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('system', 'inquiryAssignment');

-- CreateEnum
CREATE TYPE "NotificationScope" AS ENUM ('all', 'role', 'user', 'team');

-- CreateTable
CREATE TABLE "Migration" (
    "id" TEXT NOT NULL,
    "success" BOOLEAN NOT NULL,
    "sql" TEXT NOT NULL,
    "log" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Migration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MonthlyAnnouncementList" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "startedAt" TIMESTAMP(3),
    "days" INTEGER,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "MonthlyAnnouncementList_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleColor" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "nameUnique" TEXT,
    "code" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleColor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ColorsOnBodies" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "colorId" TEXT NOT NULL,
    "bodyId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ColorsOnBodies_pkey" PRIMARY KEY ("tenantId","colorId","bodyId")
);

-- CreateTable
CREATE TABLE "BicycleCondition" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleCondition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ConditionsOnBodies" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "conditionId" TEXT NOT NULL,
    "bodyId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ConditionsOnBodies_pkey" PRIMARY KEY ("tenantId","conditionId","bodyId")
);

-- CreateTable
CREATE TABLE "BicycleMaker" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleMaker_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleName" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "makerId" TEXT,
    "code" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleName_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleStyle" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleStyle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleStyleImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "styleId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleStyleImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleReadLog" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "userId" TEXT NOT NULL,
    "bicycleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BicycleReadLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleEventLog" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "userId" TEXT NOT NULL,
    "bicycleId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BicycleEventLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleMarker" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "userId" TEXT NOT NULL,
    "landmarkId" TEXT,
    "teamId" TEXT,
    "status" "BicycleMarkerStatus" NOT NULL DEFAULT 'todo',
    "isNoParkingArea" BOOLEAN NOT NULL,
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "accuracy" DOUBLE PRECISION,
    "prefecture" TEXT,
    "city" TEXT,
    "town" TEXT,
    "furtherAddress" TEXT,
    "memo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleMarker_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleMarkerImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "markerId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleMarkerImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Bicycle" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "status" "BicycleStatus" NOT NULL DEFAULT 'store',
    "type" "BicycleType",
    "available" BOOLEAN,
    "locationId" TEXT,
    "bodyId" TEXT,
    "serialTagId" TEXT,
    "theftReportId" TEXT,
    "ownerId" TEXT,
    "notificationId" TEXT,
    "announcementId" TEXT,
    "deadlineId" TEXT,
    "assessmentId" TEXT,
    "storageLocationId" TEXT,
    "teamRefId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Bicycle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleEvent" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "bicycleId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "locationId" TEXT,
    "bodyId" TEXT,
    "serialTagId" TEXT,
    "theftReportId" TEXT,
    "ownerId" TEXT,
    "notificationId" TEXT,
    "announcementId" TEXT,
    "deadlineId" TEXT,
    "assessmentId" TEXT,
    "storageLocationId" TEXT,
    "teamRefId" TEXT,
    "destRegistrationNumberId" TEXT,
    "monthlyAnnouncementListId" TEXT,
    "sellContractId" TEXT,
    "transferContractId" TEXT,
    "disposeContractId" TEXT,
    "noPaymentReasonId" TEXT,
    "type" "BicycleEventType" NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "memo" TEXT,
    "cancelId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleStoragePeriod" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "bicycleId" TEXT NOT NULL,
    "storageId" TEXT,
    "from" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "to" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleStoragePeriod_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleLocationMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleLocationMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleLocation" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "landmarkId" TEXT,
    "isNoParkingArea" BOOLEAN NOT NULL,
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "accuracy" DOUBLE PRECISION,
    "prefecture" TEXT,
    "city" TEXT,
    "town" TEXT,
    "furtherAddress" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleLocation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleBodyMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleBodyMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleBody" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "styleId" TEXT,
    "makerId" TEXT,
    "bicycleNameId" TEXT,
    "type" "BicycleType",
    "available" BOOLEAN,
    "hasBasket" BOOLEAN,
    "isLocked" BOOLEAN,
    "free1" TEXT,
    "free2" TEXT,
    "registrationNumber" TEXT,
    "registrationNumberPrefectureCode" TEXT,
    "registrationNumberPoliceCode" TEXT,
    "registrationNumber1" TEXT,
    "registrationNumber2" TEXT,
    "serialNumber" TEXT,
    "numberPlate" TEXT,
    "numberPlateTypeCode" TEXT,
    "numberPlateDltbName" TEXT,
    "numberPlateObjectiveCode" TEXT,
    "numberPlateNumberSection" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleBody_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleNotificationMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleNotificationMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleNotification" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "date" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleAnnouncementMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleAnnouncementMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleAnnouncement" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "start" TIMESTAMP(3),
    "end" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleAnnouncement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleDeadlineMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleDeadlineMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleDeadline" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "date" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleDeadline_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleAssessmentMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleAssessmentMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceSuggestion" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "price" INTEGER NOT NULL,
    "description" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PriceSuggestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleAssessment" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "price" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleAssessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TheftReportMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "TheftReportMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TheftReport" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "reported" BOOLEAN,
    "reportedAt" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "TheftReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleSerialTagMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleSerialTagMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleSerialTag" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "storageId" TEXT,
    "landmarkId" TEXT,
    "originalId" TEXT,
    "partitionKey" TEXT NOT NULL,
    "serialNo" TEXT NOT NULL,
    "seq" INTEGER NOT NULL,
    "seqUnique" INTEGER,
    "fiscalYear" TEXT NOT NULL,
    "year" TEXT NOT NULL,
    "month" TEXT NOT NULL,
    "day" TEXT NOT NULL,
    "type" "BicycleType",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleSerialTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleOwnerMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "activeId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleOwnerMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleOwner" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "sourceType" "SourceType" NOT NULL,
    "name" TEXT,
    "postalCode" TEXT,
    "address" TEXT,
    "tel" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleOwner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleStorageLocationMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleStorageLocationMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleStorageLocation" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "storageId" TEXT NOT NULL,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleStorageLocation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TeamRefMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "TeamRefMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TeamRef" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "refId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "TeamRef_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StorageFeePayment" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "fee" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StorageFeePayment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleRegistrationNumber" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "registrationNumber" TEXT NOT NULL,
    "raw" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleRegistrationNumber_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleSellContract" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "dealerId" TEXT,
    "status" "BicycleContractStatus" NOT NULL,
    "price" INTEGER,
    "date" TIMESTAMP(3),
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleSellContract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleTransferContract" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "dealerId" TEXT,
    "status" "BicycleContractStatus" NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleTransferContract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleDisposeContract" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "dealerId" TEXT,
    "status" "BicycleContractStatus" NOT NULL,
    "cost" INTEGER NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleDisposeContract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BicycleDealer" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "originalId" TEXT,
    "dealTypes" "DealType"[],
    "name" TEXT NOT NULL,
    "nameUnique" TEXT,
    "address" TEXT,
    "email" TEXT,
    "tel" TEXT,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleDealer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DltbContact" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "numberPlateLocationId" TEXT NOT NULL,
    "postalCode" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "organization" TEXT NOT NULL,
    "department" TEXT NOT NULL,
    "person" TEXT,
    "tel" TEXT,
    "fax" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "DltbContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Holiday" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "date" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Holiday_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Inquiry" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "originalId" TEXT,
    "status" "InquiryStatus",
    "urgencyId" TEXT,
    "bicycleId" TEXT,
    "receptionRouteId" TEXT,
    "receiverId" TEXT,
    "fiscalYear" INTEGER,
    "no" INTEGER,
    "receivedAt" TIMESTAMP(3),
    "title" VARCHAR(100) NOT NULL,
    "description" VARCHAR(1000) NOT NULL,
    "address" VARCHAR(100),
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "memo" VARCHAR(1000),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Inquiry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InquiryImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "inquiryId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "InquiryImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InquiryComment" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "inquiryId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "body" VARCHAR(1000) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "InquiryComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InquiryCommentImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "commentId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "InquiryCommentImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReceptionRoute" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ReceptionRoute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InquiriesOnUsers" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "inquiryId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InquiriesOnUsers_pkey" PRIMARY KEY ("tenantId","inquiryId","userId")
);

-- CreateTable
CREATE TABLE "InquiriesOnTeams" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "inquiryId" TEXT NOT NULL,
    "teamId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InquiriesOnTeams_pkey" PRIMARY KEY ("tenantId","inquiryId","teamId")
);

-- CreateTable
CREATE TABLE "InquiryUrgency" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "colorCode" VARCHAR(9) NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "InquiryUrgency_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Landmark" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "code" INTEGER NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "address" TEXT NOT NULL,
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Landmark_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LandmarkImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "landmarkId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "LandmarkImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RemoveRule" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "bicycleTypes" "BicycleType"[],
    "storageId" TEXT NOT NULL,
    "landmarkId" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RemoveRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MotorizedBicycleNumberPlateContact" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "numberPlateLocationId" TEXT NOT NULL,
    "postalCode" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "organization" TEXT NOT NULL,
    "department" TEXT NOT NULL,
    "person" TEXT,
    "tel" TEXT,
    "fax" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "MotorizedBicycleNumberPlateContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NumberPlateLocation" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "prefecture" "Prefecture" NOT NULL,
    "dltb" TEXT NOT NULL,
    "dltbBranch" TEXT NOT NULL,
    "areas" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "NumberPlateLocation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Parking" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "landmarkId" TEXT,
    "name" TEXT NOT NULL,
    "code" INTEGER NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "address" TEXT NOT NULL,
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "memo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Parking_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PoliceStation" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "prefecture" "Prefecture" NOT NULL,
    "prefectureRaw" TEXT NOT NULL,
    "prefectureCode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PoliceStation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PostalData" (
    "code" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "index" INTEGER,
    "owner" "PostalDataOwner" NOT NULL,
    "prefecture" "Prefecture" NOT NULL,
    "prefectureKana" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "cityKana" TEXT NOT NULL,
    "townArea" TEXT NOT NULL,
    "townAreaRaw" TEXT,
    "townAreaKana" TEXT NOT NULL,
    "hasChome" BOOLEAN NOT NULL,
    "chomeList" INTEGER[],
    "nationalLocalPublicEntityCode" TEXT NOT NULL,
    "oldPostalCode" TEXT NOT NULL,
    "hasAnotherPostalCode" BOOLEAN NOT NULL,
    "isNumberedForEachKoaza" BOOLEAN NOT NULL,
    "hasMultipleTownArea" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PostalData_pkey" PRIMARY KEY ("tenantId","code")
);

-- CreateTable
CREATE TABLE "PoliceReference" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "prefecture" "Prefecture" NOT NULL,
    "prefectureCode" TEXT NOT NULL,
    "status" "ReferenceStatus" NOT NULL DEFAULT 'request',
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "respondedAt" TIMESTAMP(3),
    "memo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PoliceReference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PoliceRequest" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "referenceId" TEXT NOT NULL,
    "no" INTEGER NOT NULL,
    "serialNo" TEXT NOT NULL,
    "registrationNumber" TEXT,
    "serialNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PoliceRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PoliceResponse" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "referenceId" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "status" "ResponseStatus" NOT NULL,
    "name" TEXT,
    "postalCode" TEXT,
    "address" TEXT,
    "theftReport" BOOLEAN,
    "theftReportedAt" TEXT,
    "receiptNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PoliceResponse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DltbReference" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "status" "ReferenceStatus" NOT NULL DEFAULT 'request',
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "respondedAt" TIMESTAMP(3),
    "memo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "DltbReference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DltbRequest" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "referenceId" TEXT NOT NULL,
    "no" INTEGER NOT NULL,
    "serialNo" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "DltbRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DltbResponse" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "eventId" TEXT NOT NULL,
    "referenceId" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "status" "ResponseStatus" NOT NULL,
    "name" TEXT,
    "postalCode" TEXT,
    "address" TEXT,
    "receiptNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "DltbResponse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RegistrationNumberContact" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "policeStationId" TEXT NOT NULL,
    "postalCode" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "organization" TEXT NOT NULL,
    "department" TEXT NOT NULL,
    "person" TEXT NOT NULL,
    "tel" TEXT,
    "fax" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "RegistrationNumberContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RegistrationNumberMeta" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "lastId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "RegistrationNumberMeta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RegistrationNumber" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "metaId" TEXT NOT NULL,
    "srcEventId" TEXT NOT NULL,
    "registrationNumber" TEXT NOT NULL,
    "sourceType" "SourceType" NOT NULL,
    "name" TEXT,
    "postalCode" TEXT,
    "address" TEXT,
    "tel" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "RegistrationNumber_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReleaseTag" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "type" "ReleaseType" NOT NULL,
    "code" INTEGER NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ReleaseTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Storage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sectionName" TEXT,
    "managerName" TEXT,
    "code" INTEGER NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "address" TEXT NOT NULL,
    "lat" DOUBLE PRECISION,
    "lng" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Storage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StorageImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "storageId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StorageImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StorageMapImage" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "storageId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StorageMapImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Tenant" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "shortName" TEXT NOT NULL,
    "postalCode" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "subdomain" TEXT NOT NULL,
    "userPoolId" TEXT NOT NULL,
    "logoKey" TEXT,
    "primaryColor" TEXT NOT NULL DEFAULT '#007096',
    "secondaryColor" TEXT NOT NULL DEFAULT '#fe8f1d',
    "dateFormat" "DateFormatPattern" NOT NULL DEFAULT 'YYYY_MM_DD_SLASH',
    "defaultPrefecture" "Prefecture" NOT NULL,
    "defaultPoliceStationPrefectureCode" TEXT,
    "defaultPoliceStationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Label" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "group" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "visible" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Label_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ImageSettings" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "markId" TEXT,
    "findId" TEXT,
    "ensureAbandonedId" TEXT,
    "removeId" TEXT,
    "storeId" TEXT,
    "returnToOwnerId" TEXT,
    "min" INTEGER NOT NULL,
    "max" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ImageSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ImageGuide" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "imageSettingsId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ImageGuide_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MapSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "defaultLatitude" DOUBLE PRECISION NOT NULL,
    "defaultLongitude" DOUBLE PRECISION NOT NULL,
    "apiKey" TEXT NOT NULL,
    "mapId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "MapSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "PatrolSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "flow" "PatrolFlow" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PatrolSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "MarkSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "MarkSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "FindSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "requiredBicycleType" BOOLEAN NOT NULL DEFAULT true,
    "additionalBodyFields" "BodyField"[] DEFAULT ARRAY[]::"BodyField"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "FindSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "EnsureAbandonedSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "allow" BOOLEAN NOT NULL DEFAULT true,
    "requiredBicycleType" BOOLEAN NOT NULL DEFAULT true,
    "additionalBodyFields" "BodyField"[] DEFAULT ARRAY[]::"BodyField"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,
    "imageSettingsId" TEXT,

    CONSTRAINT "EnsureAbandonedSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "RemoveSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "minutes" INTEGER NOT NULL,
    "requiredBicycleType" BOOLEAN NOT NULL DEFAULT true,
    "additionalBodyFields" "BodyField"[] DEFAULT ARRAY[]::"BodyField"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "RemoveSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "NumberingSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "serialTagPaper" "SerialTagPaper" NOT NULL DEFAULT 'A4_vertical4x6',
    "serialNoPartitions" "SerialNoPartition"[] DEFAULT ARRAY['year', 'month', 'day', 'type', 'storage', 'landmark']::"SerialNoPartition"[],
    "serialNoFormat" TEXT NOT NULL DEFAULT 'yy/mm/dd typeCode storageCode landmarkCode seq',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "NumberingSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "StoreSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "allowEntryBodyInfoLater" BOOLEAN NOT NULL DEFAULT true,
    "free1" BOOLEAN NOT NULL DEFAULT true,
    "free2" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "StoreSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "ReturnToOwnerSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "allowPrepay" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ReturnToOwnerSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "PoliceReferenceSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "fileType" "PoliceReferenceFileType" NOT NULL DEFAULT 'any',
    "csvIgnoreIndexes" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "csvIgnoreLastIndexes" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "xlsxIgnoreIndexes" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "xlsxIgnoreLastIndexes" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PoliceReferenceSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "NotificationSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "deadlineDays" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "NotificationSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "AnnouncementSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "flow" "AnnouncementFlow" NOT NULL,
    "days" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "AnnouncementSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "DeadlineSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "days" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "DeadlineSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "RecycleSettings" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "days" INTEGER NOT NULL DEFAULT 180,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "RecycleSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "BicycleTypeSetting" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "type" "BicycleType" NOT NULL,
    "name" TEXT NOT NULL,
    "code" INTEGER NOT NULL,
    "storageFee" INTEGER NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "BicycleTypeSetting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReasonForNoStorageFeePayment" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ReasonForNoStorageFeePayment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "roleId" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "displayName" VARCHAR(100),
    "displayNameUnique" TEXT,
    "kana" VARCHAR(100),
    "email" TEXT,
    "emailUnique" TEXT,
    "tel" VARCHAR(20),
    "memo" VARCHAR(1000),
    "canLogin" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "sortOrder" INTEGER NOT NULL,
    "features" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SearchSet" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SearchSet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SearchItem" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "setId" TEXT NOT NULL,
    "field" "SearchField" NOT NULL,
    "type" "SearchItemType" NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SearchItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SearchSetsOnRoles" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "setId" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SearchSetsOnRoles_pkey" PRIMARY KEY ("tenantId","setId","roleId")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "roleId" TEXT,
    "teamId" TEXT,
    "inquiryId" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL,
    "scope" "NotificationScope" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NotificationsOnUsers" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "notificationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "readAt" TIMESTAMP(3),
    "hide" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NotificationsOnUsers_pkey" PRIMARY KEY ("tenantId","notificationId","userId")
);

-- CreateTable
CREATE TABLE "Team" (
    "id" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "logoKey" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Team_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TeamsOnUsers" (
    "tenantId" TEXT NOT NULL DEFAULT (current_setting('app.tenant_id'::TEXT))::TEXT,
    "teamId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TeamsOnUsers_pkey" PRIMARY KEY ("tenantId","teamId","userId")
);

-- CreateIndex
CREATE UNIQUE INDEX "BicycleColor_tenantId_nameUnique_key" ON "BicycleColor"("tenantId", "nameUnique");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleName_tenantId_code_key" ON "BicycleName"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEventLog_eventId_key" ON "BicycleEventLog"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_locationId_key" ON "Bicycle"("locationId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_bodyId_key" ON "Bicycle"("bodyId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_serialTagId_key" ON "Bicycle"("serialTagId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_theftReportId_key" ON "Bicycle"("theftReportId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_ownerId_key" ON "Bicycle"("ownerId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_notificationId_key" ON "Bicycle"("notificationId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_announcementId_key" ON "Bicycle"("announcementId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_deadlineId_key" ON "Bicycle"("deadlineId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_assessmentId_key" ON "Bicycle"("assessmentId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_storageLocationId_key" ON "Bicycle"("storageLocationId");

-- CreateIndex
CREATE UNIQUE INDEX "Bicycle_teamRefId_key" ON "Bicycle"("teamRefId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_locationId_key" ON "BicycleEvent"("locationId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_bodyId_key" ON "BicycleEvent"("bodyId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_serialTagId_key" ON "BicycleEvent"("serialTagId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_theftReportId_key" ON "BicycleEvent"("theftReportId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_ownerId_key" ON "BicycleEvent"("ownerId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_notificationId_key" ON "BicycleEvent"("notificationId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_announcementId_key" ON "BicycleEvent"("announcementId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_deadlineId_key" ON "BicycleEvent"("deadlineId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_assessmentId_key" ON "BicycleEvent"("assessmentId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_storageLocationId_key" ON "BicycleEvent"("storageLocationId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_teamRefId_key" ON "BicycleEvent"("teamRefId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleEvent_cancelId_key" ON "BicycleEvent"("cancelId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleLocationMeta_lastId_key" ON "BicycleLocationMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleBodyMeta_lastId_key" ON "BicycleBodyMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleNotificationMeta_lastId_key" ON "BicycleNotificationMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleAnnouncementMeta_lastId_key" ON "BicycleAnnouncementMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleDeadlineMeta_lastId_key" ON "BicycleDeadlineMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleAssessmentMeta_lastId_key" ON "BicycleAssessmentMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "TheftReportMeta_lastId_key" ON "TheftReportMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleSerialTagMeta_lastId_key" ON "BicycleSerialTagMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleSerialTag_tenantId_partitionKey_seqUnique_key" ON "BicycleSerialTag"("tenantId", "partitionKey", "seqUnique");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleOwnerMeta_lastId_key" ON "BicycleOwnerMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleOwnerMeta_activeId_key" ON "BicycleOwnerMeta"("activeId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleStorageLocationMeta_lastId_key" ON "BicycleStorageLocationMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "TeamRefMeta_lastId_key" ON "TeamRefMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "StorageFeePayment_eventId_key" ON "StorageFeePayment"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleDealer_tenantId_nameUnique_key" ON "BicycleDealer"("tenantId", "nameUnique");

-- CreateIndex
CREATE UNIQUE INDEX "DltbContact_numberPlateLocationId_key" ON "DltbContact"("numberPlateLocationId");

-- CreateIndex
CREATE UNIQUE INDEX "Inquiry_fiscalYear_no_key" ON "Inquiry"("fiscalYear", "no");

-- CreateIndex
CREATE UNIQUE INDEX "Landmark_tenantId_code_key" ON "Landmark"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "MotorizedBicycleNumberPlateContact_numberPlateLocationId_key" ON "MotorizedBicycleNumberPlateContact"("numberPlateLocationId");

-- CreateIndex
CREATE UNIQUE INDEX "Parking_tenantId_code_key" ON "Parking"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "PoliceRequest_eventId_key" ON "PoliceRequest"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "PoliceResponse_eventId_key" ON "PoliceResponse"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "PoliceResponse_requestId_key" ON "PoliceResponse"("requestId");

-- CreateIndex
CREATE UNIQUE INDEX "DltbRequest_eventId_key" ON "DltbRequest"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "DltbResponse_eventId_key" ON "DltbResponse"("eventId");

-- CreateIndex
CREATE UNIQUE INDEX "DltbResponse_requestId_key" ON "DltbResponse"("requestId");

-- CreateIndex
CREATE UNIQUE INDEX "RegistrationNumberContact_policeStationId_key" ON "RegistrationNumberContact"("policeStationId");

-- CreateIndex
CREATE UNIQUE INDEX "RegistrationNumberMeta_lastId_key" ON "RegistrationNumberMeta"("lastId");

-- CreateIndex
CREATE UNIQUE INDEX "RegistrationNumber_srcEventId_key" ON "RegistrationNumber"("srcEventId");

-- CreateIndex
CREATE UNIQUE INDEX "ReleaseTag_tenantId_code_key" ON "ReleaseTag"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "Storage_tenantId_code_key" ON "Storage"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "StorageMapImage_storageId_key" ON "StorageMapImage"("storageId");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_name_key" ON "Tenant"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_shortName_key" ON "Tenant"("shortName");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_subdomain_key" ON "Tenant"("subdomain");

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_defaultPoliceStationId_key" ON "Tenant"("defaultPoliceStationId");

-- CreateIndex
CREATE UNIQUE INDEX "Label_tenantId_group_key_key" ON "Label"("tenantId", "group", "key");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_markId_key" ON "ImageSettings"("markId");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_findId_key" ON "ImageSettings"("findId");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_ensureAbandonedId_key" ON "ImageSettings"("ensureAbandonedId");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_removeId_key" ON "ImageSettings"("removeId");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_storeId_key" ON "ImageSettings"("storeId");

-- CreateIndex
CREATE UNIQUE INDEX "ImageSettings_returnToOwnerId_key" ON "ImageSettings"("returnToOwnerId");

-- CreateIndex
CREATE UNIQUE INDEX "MapSettings_tenantId_key" ON "MapSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "PatrolSettings_tenantId_key" ON "PatrolSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "MarkSettings_tenantId_key" ON "MarkSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "FindSettings_tenantId_key" ON "FindSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "EnsureAbandonedSettings_tenantId_key" ON "EnsureAbandonedSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "RemoveSettings_tenantId_key" ON "RemoveSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "NumberingSettings_tenantId_key" ON "NumberingSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "StoreSettings_tenantId_key" ON "StoreSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "ReturnToOwnerSettings_tenantId_key" ON "ReturnToOwnerSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "PoliceReferenceSettings_tenantId_key" ON "PoliceReferenceSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "NotificationSettings_tenantId_key" ON "NotificationSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "AnnouncementSettings_tenantId_key" ON "AnnouncementSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "DeadlineSettings_tenantId_key" ON "DeadlineSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "RecycleSettings_tenantId_key" ON "RecycleSettings"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleTypeSetting_tenantId_type_key" ON "BicycleTypeSetting"("tenantId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "BicycleTypeSetting_tenantId_code_key" ON "BicycleTypeSetting"("tenantId", "code");

-- CreateIndex
CREATE UNIQUE INDEX "User_tenantId_displayNameUnique_key" ON "User"("tenantId", "displayNameUnique");

-- CreateIndex
CREATE UNIQUE INDEX "User_tenantId_emailUnique_key" ON "User"("tenantId", "emailUnique");

-- AddForeignKey
ALTER TABLE "BicycleColor" ADD CONSTRAINT "BicycleColor_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ColorsOnBodies" ADD CONSTRAINT "ColorsOnBodies_colorId_fkey" FOREIGN KEY ("colorId") REFERENCES "BicycleColor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ColorsOnBodies" ADD CONSTRAINT "ColorsOnBodies_bodyId_fkey" FOREIGN KEY ("bodyId") REFERENCES "BicycleBody"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleCondition" ADD CONSTRAINT "BicycleCondition_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConditionsOnBodies" ADD CONSTRAINT "ConditionsOnBodies_conditionId_fkey" FOREIGN KEY ("conditionId") REFERENCES "BicycleCondition"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConditionsOnBodies" ADD CONSTRAINT "ConditionsOnBodies_bodyId_fkey" FOREIGN KEY ("bodyId") REFERENCES "BicycleBody"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleMaker" ADD CONSTRAINT "BicycleMaker_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleName" ADD CONSTRAINT "BicycleName_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleName" ADD CONSTRAINT "BicycleName_makerId_fkey" FOREIGN KEY ("makerId") REFERENCES "BicycleMaker"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStyle" ADD CONSTRAINT "BicycleStyle_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStyleImage" ADD CONSTRAINT "BicycleStyleImage_styleId_fkey" FOREIGN KEY ("styleId") REFERENCES "BicycleStyle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleReadLog" ADD CONSTRAINT "BicycleReadLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleReadLog" ADD CONSTRAINT "BicycleReadLog_bicycleId_fkey" FOREIGN KEY ("bicycleId") REFERENCES "Bicycle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEventLog" ADD CONSTRAINT "BicycleEventLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEventLog" ADD CONSTRAINT "BicycleEventLog_bicycleId_fkey" FOREIGN KEY ("bicycleId") REFERENCES "Bicycle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEventLog" ADD CONSTRAINT "BicycleEventLog_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleMarker" ADD CONSTRAINT "BicycleMarker_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleMarker" ADD CONSTRAINT "BicycleMarker_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleMarker" ADD CONSTRAINT "BicycleMarker_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleMarkerImage" ADD CONSTRAINT "BicycleMarkerImage_markerId_fkey" FOREIGN KEY ("markerId") REFERENCES "BicycleMarker"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "BicycleLocationMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_bodyId_fkey" FOREIGN KEY ("bodyId") REFERENCES "BicycleBodyMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_serialTagId_fkey" FOREIGN KEY ("serialTagId") REFERENCES "BicycleSerialTagMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_theftReportId_fkey" FOREIGN KEY ("theftReportId") REFERENCES "TheftReportMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "BicycleOwnerMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "BicycleNotificationMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_announcementId_fkey" FOREIGN KEY ("announcementId") REFERENCES "BicycleAnnouncementMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_deadlineId_fkey" FOREIGN KEY ("deadlineId") REFERENCES "BicycleDeadlineMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "BicycleAssessmentMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_storageLocationId_fkey" FOREIGN KEY ("storageLocationId") REFERENCES "BicycleStorageLocationMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bicycle" ADD CONSTRAINT "Bicycle_teamRefId_fkey" FOREIGN KEY ("teamRefId") REFERENCES "TeamRefMeta"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_bicycleId_fkey" FOREIGN KEY ("bicycleId") REFERENCES "Bicycle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "BicycleLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_bodyId_fkey" FOREIGN KEY ("bodyId") REFERENCES "BicycleBody"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_serialTagId_fkey" FOREIGN KEY ("serialTagId") REFERENCES "BicycleSerialTag"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_theftReportId_fkey" FOREIGN KEY ("theftReportId") REFERENCES "TheftReport"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "BicycleOwner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "BicycleNotification"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_announcementId_fkey" FOREIGN KEY ("announcementId") REFERENCES "BicycleAnnouncement"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_deadlineId_fkey" FOREIGN KEY ("deadlineId") REFERENCES "BicycleDeadline"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "BicycleAssessment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_storageLocationId_fkey" FOREIGN KEY ("storageLocationId") REFERENCES "BicycleStorageLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_teamRefId_fkey" FOREIGN KEY ("teamRefId") REFERENCES "TeamRef"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_destRegistrationNumberId_fkey" FOREIGN KEY ("destRegistrationNumberId") REFERENCES "RegistrationNumber"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_monthlyAnnouncementListId_fkey" FOREIGN KEY ("monthlyAnnouncementListId") REFERENCES "MonthlyAnnouncementList"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_sellContractId_fkey" FOREIGN KEY ("sellContractId") REFERENCES "BicycleSellContract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_transferContractId_fkey" FOREIGN KEY ("transferContractId") REFERENCES "BicycleTransferContract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_disposeContractId_fkey" FOREIGN KEY ("disposeContractId") REFERENCES "BicycleDisposeContract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_noPaymentReasonId_fkey" FOREIGN KEY ("noPaymentReasonId") REFERENCES "ReasonForNoStorageFeePayment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleEvent" ADD CONSTRAINT "BicycleEvent_cancelId_fkey" FOREIGN KEY ("cancelId") REFERENCES "BicycleEvent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStoragePeriod" ADD CONSTRAINT "BicycleStoragePeriod_bicycleId_fkey" FOREIGN KEY ("bicycleId") REFERENCES "Bicycle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStoragePeriod" ADD CONSTRAINT "BicycleStoragePeriod_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleImage" ADD CONSTRAINT "BicycleImage_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleLocationMeta" ADD CONSTRAINT "BicycleLocationMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleLocation" ADD CONSTRAINT "BicycleLocation_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleLocationMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleLocation" ADD CONSTRAINT "BicycleLocation_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleBodyMeta" ADD CONSTRAINT "BicycleBodyMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleBody"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleBody" ADD CONSTRAINT "BicycleBody_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleBodyMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleBody" ADD CONSTRAINT "BicycleBody_styleId_fkey" FOREIGN KEY ("styleId") REFERENCES "BicycleStyle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleBody" ADD CONSTRAINT "BicycleBody_makerId_fkey" FOREIGN KEY ("makerId") REFERENCES "BicycleMaker"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleBody" ADD CONSTRAINT "BicycleBody_bicycleNameId_fkey" FOREIGN KEY ("bicycleNameId") REFERENCES "BicycleName"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleNotificationMeta" ADD CONSTRAINT "BicycleNotificationMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleNotification"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleNotification" ADD CONSTRAINT "BicycleNotification_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleNotificationMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleAnnouncementMeta" ADD CONSTRAINT "BicycleAnnouncementMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleAnnouncement"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleAnnouncement" ADD CONSTRAINT "BicycleAnnouncement_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleAnnouncementMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleDeadlineMeta" ADD CONSTRAINT "BicycleDeadlineMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleDeadline"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleDeadline" ADD CONSTRAINT "BicycleDeadline_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleDeadlineMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleAssessmentMeta" ADD CONSTRAINT "BicycleAssessmentMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleAssessment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PriceSuggestion" ADD CONSTRAINT "PriceSuggestion_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleAssessment" ADD CONSTRAINT "BicycleAssessment_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleAssessmentMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TheftReportMeta" ADD CONSTRAINT "TheftReportMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "TheftReport"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TheftReport" ADD CONSTRAINT "TheftReport_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "TheftReportMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSerialTagMeta" ADD CONSTRAINT "BicycleSerialTagMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleSerialTag"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSerialTag" ADD CONSTRAINT "BicycleSerialTag_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleSerialTagMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSerialTag" ADD CONSTRAINT "BicycleSerialTag_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSerialTag" ADD CONSTRAINT "BicycleSerialTag_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSerialTag" ADD CONSTRAINT "BicycleSerialTag_originalId_fkey" FOREIGN KEY ("originalId") REFERENCES "BicycleSerialTag"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleOwnerMeta" ADD CONSTRAINT "BicycleOwnerMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleOwner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleOwnerMeta" ADD CONSTRAINT "BicycleOwnerMeta_activeId_fkey" FOREIGN KEY ("activeId") REFERENCES "BicycleOwner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleOwner" ADD CONSTRAINT "BicycleOwner_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleOwnerMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStorageLocationMeta" ADD CONSTRAINT "BicycleStorageLocationMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "BicycleStorageLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStorageLocation" ADD CONSTRAINT "BicycleStorageLocation_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "BicycleStorageLocationMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleStorageLocation" ADD CONSTRAINT "BicycleStorageLocation_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamRefMeta" ADD CONSTRAINT "TeamRefMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "TeamRef"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamRef" ADD CONSTRAINT "TeamRef_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "TeamRefMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamRef" ADD CONSTRAINT "TeamRef_refId_fkey" FOREIGN KEY ("refId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StorageFeePayment" ADD CONSTRAINT "StorageFeePayment_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleSellContract" ADD CONSTRAINT "BicycleSellContract_dealerId_fkey" FOREIGN KEY ("dealerId") REFERENCES "BicycleDealer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleTransferContract" ADD CONSTRAINT "BicycleTransferContract_dealerId_fkey" FOREIGN KEY ("dealerId") REFERENCES "BicycleDealer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleDisposeContract" ADD CONSTRAINT "BicycleDisposeContract_dealerId_fkey" FOREIGN KEY ("dealerId") REFERENCES "BicycleDealer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleDealer" ADD CONSTRAINT "BicycleDealer_originalId_fkey" FOREIGN KEY ("originalId") REFERENCES "BicycleDealer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbContact" ADD CONSTRAINT "DltbContact_numberPlateLocationId_fkey" FOREIGN KEY ("numberPlateLocationId") REFERENCES "NumberPlateLocation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Holiday" ADD CONSTRAINT "Holiday_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inquiry" ADD CONSTRAINT "Inquiry_originalId_fkey" FOREIGN KEY ("originalId") REFERENCES "Inquiry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inquiry" ADD CONSTRAINT "Inquiry_urgencyId_fkey" FOREIGN KEY ("urgencyId") REFERENCES "InquiryUrgency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inquiry" ADD CONSTRAINT "Inquiry_bicycleId_fkey" FOREIGN KEY ("bicycleId") REFERENCES "Bicycle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inquiry" ADD CONSTRAINT "Inquiry_receptionRouteId_fkey" FOREIGN KEY ("receptionRouteId") REFERENCES "ReceptionRoute"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Inquiry" ADD CONSTRAINT "Inquiry_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiryImage" ADD CONSTRAINT "InquiryImage_inquiryId_fkey" FOREIGN KEY ("inquiryId") REFERENCES "Inquiry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiryComment" ADD CONSTRAINT "InquiryComment_inquiryId_fkey" FOREIGN KEY ("inquiryId") REFERENCES "Inquiry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiryComment" ADD CONSTRAINT "InquiryComment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiryCommentImage" ADD CONSTRAINT "InquiryCommentImage_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "InquiryComment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReceptionRoute" ADD CONSTRAINT "ReceptionRoute_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiriesOnUsers" ADD CONSTRAINT "InquiriesOnUsers_inquiryId_fkey" FOREIGN KEY ("inquiryId") REFERENCES "Inquiry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiriesOnUsers" ADD CONSTRAINT "InquiriesOnUsers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiriesOnTeams" ADD CONSTRAINT "InquiriesOnTeams_inquiryId_fkey" FOREIGN KEY ("inquiryId") REFERENCES "Inquiry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiriesOnTeams" ADD CONSTRAINT "InquiriesOnTeams_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InquiryUrgency" ADD CONSTRAINT "InquiryUrgency_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Landmark" ADD CONSTRAINT "Landmark_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LandmarkImage" ADD CONSTRAINT "LandmarkImage_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemoveRule" ADD CONSTRAINT "RemoveRule_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemoveRule" ADD CONSTRAINT "RemoveRule_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemoveRule" ADD CONSTRAINT "RemoveRule_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MotorizedBicycleNumberPlateContact" ADD CONSTRAINT "MotorizedBicycleNumberPlateContact_numberPlateLocationId_fkey" FOREIGN KEY ("numberPlateLocationId") REFERENCES "NumberPlateLocation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NumberPlateLocation" ADD CONSTRAINT "NumberPlateLocation_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Parking" ADD CONSTRAINT "Parking_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Parking" ADD CONSTRAINT "Parking_landmarkId_fkey" FOREIGN KEY ("landmarkId") REFERENCES "Landmark"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceStation" ADD CONSTRAINT "PoliceStation_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceRequest" ADD CONSTRAINT "PoliceRequest_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceRequest" ADD CONSTRAINT "PoliceRequest_referenceId_fkey" FOREIGN KEY ("referenceId") REFERENCES "PoliceReference"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceResponse" ADD CONSTRAINT "PoliceResponse_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceResponse" ADD CONSTRAINT "PoliceResponse_referenceId_fkey" FOREIGN KEY ("referenceId") REFERENCES "PoliceReference"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceResponse" ADD CONSTRAINT "PoliceResponse_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "PoliceRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbRequest" ADD CONSTRAINT "DltbRequest_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbRequest" ADD CONSTRAINT "DltbRequest_referenceId_fkey" FOREIGN KEY ("referenceId") REFERENCES "DltbReference"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbResponse" ADD CONSTRAINT "DltbResponse_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbResponse" ADD CONSTRAINT "DltbResponse_referenceId_fkey" FOREIGN KEY ("referenceId") REFERENCES "DltbReference"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DltbResponse" ADD CONSTRAINT "DltbResponse_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "DltbRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegistrationNumberContact" ADD CONSTRAINT "RegistrationNumberContact_policeStationId_fkey" FOREIGN KEY ("policeStationId") REFERENCES "PoliceStation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegistrationNumberMeta" ADD CONSTRAINT "RegistrationNumberMeta_lastId_fkey" FOREIGN KEY ("lastId") REFERENCES "RegistrationNumber"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegistrationNumber" ADD CONSTRAINT "RegistrationNumber_metaId_fkey" FOREIGN KEY ("metaId") REFERENCES "RegistrationNumberMeta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RegistrationNumber" ADD CONSTRAINT "RegistrationNumber_srcEventId_fkey" FOREIGN KEY ("srcEventId") REFERENCES "BicycleEvent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReleaseTag" ADD CONSTRAINT "ReleaseTag_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Storage" ADD CONSTRAINT "Storage_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StorageImage" ADD CONSTRAINT "StorageImage_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StorageMapImage" ADD CONSTRAINT "StorageMapImage_storageId_fkey" FOREIGN KEY ("storageId") REFERENCES "Storage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Tenant" ADD CONSTRAINT "Tenant_defaultPoliceStationId_fkey" FOREIGN KEY ("defaultPoliceStationId") REFERENCES "PoliceStation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Label" ADD CONSTRAINT "Label_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_markId_fkey" FOREIGN KEY ("markId") REFERENCES "MarkSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_findId_fkey" FOREIGN KEY ("findId") REFERENCES "FindSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_ensureAbandonedId_fkey" FOREIGN KEY ("ensureAbandonedId") REFERENCES "EnsureAbandonedSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_removeId_fkey" FOREIGN KEY ("removeId") REFERENCES "RemoveSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_storeId_fkey" FOREIGN KEY ("storeId") REFERENCES "StoreSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageSettings" ADD CONSTRAINT "ImageSettings_returnToOwnerId_fkey" FOREIGN KEY ("returnToOwnerId") REFERENCES "ReturnToOwnerSettings"("tenantId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImageGuide" ADD CONSTRAINT "ImageGuide_imageSettingsId_fkey" FOREIGN KEY ("imageSettingsId") REFERENCES "ImageSettings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MapSettings" ADD CONSTRAINT "MapSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PatrolSettings" ADD CONSTRAINT "PatrolSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MarkSettings" ADD CONSTRAINT "MarkSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FindSettings" ADD CONSTRAINT "FindSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EnsureAbandonedSettings" ADD CONSTRAINT "EnsureAbandonedSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemoveSettings" ADD CONSTRAINT "RemoveSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NumberingSettings" ADD CONSTRAINT "NumberingSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StoreSettings" ADD CONSTRAINT "StoreSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnToOwnerSettings" ADD CONSTRAINT "ReturnToOwnerSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PoliceReferenceSettings" ADD CONSTRAINT "PoliceReferenceSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationSettings" ADD CONSTRAINT "NotificationSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnnouncementSettings" ADD CONSTRAINT "AnnouncementSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DeadlineSettings" ADD CONSTRAINT "DeadlineSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RecycleSettings" ADD CONSTRAINT "RecycleSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BicycleTypeSetting" ADD CONSTRAINT "BicycleTypeSetting_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReasonForNoStorageFeePayment" ADD CONSTRAINT "ReasonForNoStorageFeePayment_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Role" ADD CONSTRAINT "Role_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SearchItem" ADD CONSTRAINT "SearchItem_setId_fkey" FOREIGN KEY ("setId") REFERENCES "SearchSet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SearchSetsOnRoles" ADD CONSTRAINT "SearchSetsOnRoles_setId_fkey" FOREIGN KEY ("setId") REFERENCES "SearchSet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SearchSetsOnRoles" ADD CONSTRAINT "SearchSetsOnRoles_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_inquiryId_fkey" FOREIGN KEY ("inquiryId") REFERENCES "Inquiry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationsOnUsers" ADD CONSTRAINT "NotificationsOnUsers_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "Notification"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationsOnUsers" ADD CONSTRAINT "NotificationsOnUsers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamsOnUsers" ADD CONSTRAINT "TeamsOnUsers_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeamsOnUsers" ADD CONSTRAINT "TeamsOnUsers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
