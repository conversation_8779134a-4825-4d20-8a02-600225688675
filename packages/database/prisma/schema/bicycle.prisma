model Bicycle {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // 必須フィールド
  status BicycleStatus @default(store)

  // オプショナルフィールド
  type      BicycleType?
  available Boolean?

  // リレーション
  events    BicycleEvent[]
  periods   BicycleStoragePeriod[]
  inquiries Inquiry[]
  readLogs  BicycleReadLog[]
  eventLogs BicycleEventLog[]

  // バージョニング関連のリレーション
  locationId        String?                     @unique
  location          BicycleLocationMeta?        @relation(fields: [locationId], references: [id])
  bodyId            String?                     @unique
  body              BicycleBodyMeta?            @relation(fields: [bodyId], references: [id])
  serialTagId       String?                     @unique
  serialTag         BicycleSerialTagMeta?       @relation(fields: [serialTagId], references: [id])
  theftReportId     String?                     @unique
  theftReport       TheftReportMeta?            @relation(fields: [theftReportId], references: [id])
  ownerId           String?                     @unique
  owner             BicycleOwnerMeta?           @relation(fields: [ownerId], references: [id])
  notificationId    String?                     @unique
  notification      BicycleNotificationMeta?    @relation(fields: [notificationId], references: [id])
  announcementId    String?                     @unique
  announcement      BicycleAnnouncementMeta?    @relation(fields: [announcementId], references: [id])
  deadlineId        String?                     @unique
  deadline          BicycleDeadlineMeta?        @relation(fields: [deadlineId], references: [id])
  assessmentId      String?                     @unique
  assessment        BicycleAssessmentMeta?      @relation(fields: [assessmentId], references: [id])
  storageLocationId String?                     @unique
  storageLocation   BicycleStorageLocationMeta? @relation(fields: [storageLocationId], references: [id])
  teamRefId         String?                     @unique
  team              TeamRefMeta?                @relation(fields: [teamRefId], references: [id])

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleEvent {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  bicycleId String
  bicycle   Bicycle @relation(fields: [bicycleId], references: [id])
  userId    String
  user      User    @relation(fields: [userId], references: [id])

  eventLog BicycleEventLog?

  locationId        String?                 @unique
  location          BicycleLocation?        @relation(fields: [locationId], references: [id])
  bodyId            String?                 @unique
  body              BicycleBody?            @relation(fields: [bodyId], references: [id])
  serialTagId       String?                 @unique
  serialTag         BicycleSerialTag?       @relation(fields: [serialTagId], references: [id])
  theftReportId     String?                 @unique
  theftReport       TheftReport?            @relation(fields: [theftReportId], references: [id])
  ownerId           String?                 @unique
  owner             BicycleOwner?           @relation(fields: [ownerId], references: [id])
  notificationId    String?                 @unique
  notification      BicycleNotification?    @relation(fields: [notificationId], references: [id])
  announcementId    String?                 @unique
  announcement      BicycleAnnouncement?    @relation(fields: [announcementId], references: [id])
  deadlineId        String?                 @unique
  deadline          BicycleDeadline?        @relation(fields: [deadlineId], references: [id])
  assessmentId      String?                 @unique
  assessment        BicycleAssessment?      @relation(fields: [assessmentId], references: [id])
  storageLocationId String?                 @unique
  storageLocation   BicycleStorageLocation? @relation(fields: [storageLocationId], references: [id])
  teamRefId         String?                 @unique
  team              TeamRef?                @relation(fields: [teamRefId], references: [id])

  srcRegistrationNumber    RegistrationNumber? @relation("source")
  destRegistrationNumberId String?
  destRegistrationNumber   RegistrationNumber? @relation("destination", fields: [destRegistrationNumberId], references: [id])

  monthlyAnnouncementListId String?
  monthlyAnnouncementList   MonthlyAnnouncementList? @relation(fields: [monthlyAnnouncementListId], references: [id])

  // 処分契約のリレーション
  sellContractId     String?
  sellContract       BicycleSellContract?     @relation(fields: [sellContractId], references: [id])
  transferContractId String?
  transferContract   BicycleTransferContract? @relation(fields: [transferContractId], references: [id])
  disposeContractId  String?
  disposeContract    BicycleDisposeContract?  @relation(fields: [disposeContractId], references: [id])

  // イベントのリレーション
  policeRequest     PoliceRequest?
  policeResponse    PoliceResponse?
  dltbRequest       DltbRequest?
  dltbResponse      DltbResponse?
  storageFeePayment StorageFeePayment?

  noPaymentReasonId String?
  noPaymentReason   ReasonForNoStorageFeePayment? @relation(fields: [noPaymentReasonId], references: [id])

  /// イベントタイプ
  type BicycleEventType
  /// イベント日時
  date DateTime         @default(now())
  /// メモ
  memo String?

  images BicycleImage[]

  cancelId String?       @unique
  cancel   BicycleEvent? @relation("cancel", fields: [cancelId], references: [id])
  cancelBy BicycleEvent? @relation("cancel")
  // TODO: 以下は新しいキャンセル関連フィールド
  // cancelById String?
  // cancelBy   BicycleEvent?  @relation("cancel", fields: [cancelById], references: [id])
  // cancel     BicycleEvent[] @relation("cancel")

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleStoragePeriod {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  bicycleId String
  bicycle   Bicycle @relation(fields: [bicycleId], references: [id])

  storageId String?
  storage   Storage? @relation(fields: [storageId], references: [id])

  from DateTime  @default(now())
  to   DateTime?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleImage {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  eventId String
  event   BicycleEvent @relation(fields: [eventId], references: [id])

  /// 画像を格納したS3キー
  key         String
  /// 説明
  description String
  /// 表示順
  sortOrder   Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleLocationMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleLocation[] @relation("versions")

  lastId String?          @unique
  last   BicycleLocation? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleLocation {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleLocationMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleLocationMeta? @relation("last")

  // リレーション
  event      BicycleEvent?
  landmarkId String?
  landmark   Landmark?     @relation(fields: [landmarkId], references: [id])

  /// 放置禁止区域
  isNoParkingArea Boolean

  /// 緯度
  lat      Float?
  /// 経度
  lng      Float?
  /// 緯度経度の精度（緯度経度を手動で調整した場合は設定しない）
  accuracy Float?

  /// 都道府県
  prefecture     String?
  /// 市区町村
  city           String?
  /// 町名
  town           String?
  /// 丁目・番地
  furtherAddress String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleBodyMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleBody[] @relation("versions")

  lastId String?      @unique
  last   BicycleBody? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleBody {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleBodyMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleBodyMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  styleId String?
  style   BicycleStyle? @relation(fields: [styleId], references: [id])

  makerId String?
  maker   BicycleMaker? @relation(fields: [makerId], references: [id])

  bicycleNameId String?
  bicycleName   BicycleName? @relation(fields: [bicycleNameId], references: [id])

  conditions ConditionsOnBodies[]

  /// 車両区分
  type      BicycleType?
  /// 使用可能か
  available Boolean?
  /// 色
  colors    ColorsOnBodies[]
  /// かごの有無
  hasBasket Boolean?
  /// 施錠の有無
  isLocked  Boolean?
  // 自由入力欄
  free1     String?
  free2     String?

  // 防犯登録番号
  /// 防犯登録番号関連データをすべてハイフンで結合したもの
  registrationNumber               String?
  /// 防犯登録番号の都道府県コード
  registrationNumberPrefectureCode String?
  /// 防犯登録番号の警察署コード
  registrationNumberPoliceCode     String?
  /// 防犯登録番号の連番1
  registrationNumber1              String?
  /// 防犯登録番号の連番2
  registrationNumber2              String?

  /// 車体番号
  serialNumber String?

  // 陸運局照会系情報
  /// 参考: https://toyota.jp/ucar/support/buyingguide/12.html
  /// パース前のテキスト（全角スペースは半角スペースに変換済み） e.g "渋谷区 な 7934"
  numberPlate              String?
  /// 用途による区分 e.g. 1:二輪自動車、無:原付など
  numberPlateTypeCode      String?
  /// 運輸支局または自動車検査登録事務所の名称（e.g. 品川）
  numberPlateDltbName      String?
  /// 軽自動車のケース 自家用:あ〜を、レンタカー:わ、事業用:りれ
  numberPlateObjectiveCode String?
  /// 一連指定番号 e.g. 1 〜 99-99
  numberPlateNumberSection String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleNotificationMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleNotification[] @relation("versions")

  lastId String?              @unique
  last   BicycleNotification? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleNotification {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleNotificationMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleNotificationMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  // 通知日取り消しの場合は null （通知日は返還通知はがきの印刷日）
  date DateTime?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleAnnouncementMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleAnnouncement[] @relation("versions")

  lastId String?              @unique
  last   BicycleAnnouncement? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleAnnouncement {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleAnnouncementMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleAnnouncementMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  start DateTime?
  end   DateTime?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleDeadlineMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleDeadline[] @relation("versions")

  lastId String?          @unique
  last   BicycleDeadline? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleDeadline {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleDeadlineMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleDeadlineMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  /// 期限削除の場合は null
  date DateTime?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleAssessmentMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleAssessment[] @relation("versions")

  lastId String?            @unique
  last   BicycleAssessment? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model PriceSuggestion {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  price       Int
  description String?
  sortOrder   Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleAssessment {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleAssessmentMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleAssessmentMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  /// 評価額
  price Int?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model TheftReportMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions TheftReport[] @relation("versions")

  lastId String?      @unique
  last   TheftReport? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model TheftReport {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     TheftReportMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta TheftReportMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  reported   Boolean?
  /// 届出日 (yyyy-mm-ddでフォーマットする)
  reportedAt String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleSerialTagMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleSerialTag[] @relation("versions")

  lastId String?           @unique
  last   BicycleSerialTag? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleSerialTag {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleSerialTagMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleSerialTagMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  storageId  String?
  storage    Storage?  @relation(fields: [storageId], references: [id])
  landmarkId String?
  landmark   Landmark? @relation(fields: [landmarkId], references: [id])

  // 自己リレーション
  originalId String?
  original   BicycleSerialTag?  @relation("versions", fields: [originalId], references: [id])
  versions   BicycleSerialTag[] @relation("versions")

  /// partitionParamsToKey で生成されるパーティションキー
  partitionKey String

  /// 整理番号 (連番を含む e.g. 25/04/01 00 00 000 000)
  serialNo   String
  /// 連番
  seq        Int
  seqUnique  Int?
  /// 年度(4月〜翌年3月)
  fiscalYear String
  /// 年(1月〜12月)
  year       String
  month      String
  day        String
  type       BicycleType?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([tenantId, partitionKey, seqUnique])
}

model BicycleOwnerMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleOwner[] @relation("versions")

  lastId   String?       @unique
  last     BicycleOwner? @relation("last", fields: [lastId], references: [id])
  activeId String?       @unique
  active   BicycleOwner? @relation("active", fields: [activeId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleOwner {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId     String
  meta       BicycleOwnerMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta   BicycleOwnerMeta? @relation("last")
  activeMeta BicycleOwnerMeta? @relation("active")

  // リレーション
  event BicycleEvent?

  sourceType SourceType
  name       String?
  postalCode String?
  address    String?
  tel        String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BicycleStorageLocationMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions BicycleStorageLocation[] @relation("versions")

  lastId String?                 @unique
  last   BicycleStorageLocation? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

/// 保管場所
model BicycleStorageLocation {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     BicycleStorageLocationMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta BicycleStorageLocationMeta? @relation("last")

  // リレーション
  event     BicycleEvent?
  storageId String
  storage   Storage       @relation(fields: [storageId], references: [id])

  memo String?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model TeamRefMeta {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  versions TeamRef[] @relation("versions")

  lastId String?  @unique
  last   TeamRef? @relation("last", fields: [lastId], references: [id])

  bicycle Bicycle?

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model TeamRef {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  metaId   String
  meta     TeamRefMeta  @relation("versions", fields: [metaId], references: [id])
  lastMeta TeamRefMeta? @relation("last")

  // リレーション
  event BicycleEvent?

  refId String
  ref   Team   @relation(fields: [refId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model StorageFeePayment {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  // リレーション
  eventId String       @unique
  event   BicycleEvent @relation(fields: [eventId], references: [id])

  fee Int

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// enums
enum BicycleStatus {
  /// 発見
  find
  /// 放置確認済み
  ensureAbandoned
  // 撤去前に見失った
  lose
  // 対応しなかった
  ignore
  /// 撤去
  remove
  /// 保管
  store
  /// 返還
  returnToOwner
  /// 売却
  sell
  /// 譲渡
  transfer
  /// 廃棄
  dispose
  /// リサイクル
  recycle
  /// 紛失
  loseFromStorage
}

enum BicycleEventType {
  // 発見〜撤去〜保管に関連するイベント
  /// 発見
  find
  /// 放置確認済み
  ensureAbandoned
  // 撤去前に見失った
  lose
  // 対応しなかった
  ignore
  /// 撤去
  remove
  /// 整理番号付与
  numbering
  /// 整理番号の再採番
  reNumbering
  /// 整理番号札の
  printSerialTag
  /// 保管
  store

  // 保管後のイベント
  /// 査定
  assess
  /// 所有者情報コピー
  copyOwnerInfo
  /// 公示
  appendAnnouncementList
  removeAnnouncementList
  startAnnouncement
  updateAnnouncement
  cancelAnnouncement
  /// 警察照会
  requestPolice
  receivePolice
  cancelPolice
  /// 陸運局照会 (陸運局: District Land Transport Bureaus)
  requestDltb
  receiveDltb
  cancelDltb
  /// 所有者への通知
  notify
  updateNotification
  cancelNotification
  resetNotificationDate
  /// 保管期限
  extendDeadline
  scheduleDeadline
  updateDeadline
  cancelDeadline
  /// 保管所から保管所への移動予定
  scheduleMoveBetweenStorage
  /// 保管所から保管所への移動
  moveBetweenStorage

  // 返還関連イベント
  /// 返還
  returnToOwner
  /// 返還キャンセル
  cancelReturnToOwner
  /// 保管料収納
  collectStorageFee
  /// 保管料収納キャンセル
  cancelCollectStorageFee

  // 売却関連イベント
  /// 売却
  sell
  /// 売却キャンセル
  cancelSell
  /// 売却予定
  scheduleSell
  /// 売却予定編集
  editScheduleSell
  /// 売却予定キャンセル
  cancelSellSchedule

  // 譲渡関連イベント
  /// 譲渡
  transfer
  /// 譲渡キャンセル
  cancelTransfer
  /// 譲渡予定
  scheduleTransfer
  /// 譲渡予定キャンセル
  cancelTransferSchedule

  // 廃棄関連イベント
  /// 廃棄
  dispose
  /// 廃棄キャンセル
  cancelDispose
  /// 廃棄予定
  scheduleDispose
  /// 廃棄予定キャンセル
  cancelDisposeSchedule

  // リサイクル関連イベント
  /// リサイクル
  recycle
  /// リサイクルキャンセル
  cancelRecycle
  /// リサイクル予定
  scheduleRecycle
  /// リサイクル予定キャンセル
  cancelRecycleSchedule

  // 紛失関連イベント
  /// 紛失
  loseFromStorage
  /// 紛失キャンセル
  cancelLoseFromStorage

  // その他のイベント
  create
  /// 編集
  editLocation
  editBody
  editOwner
}

enum BicycleType {
  /// 自転車
  bicycle
  /// 原動機付き自転車
  motorizedBicycle
  /// 自動二輪車
  motorcycle
  /// 特定小型原動機付自転車（電動キックボード）
  specifiedSmallMotorizedBicycle
  /// その他
  other
}

/// TODO 防犯登録番号のサンプル
model BicycleRegistrationNumber {
  id       String @id @default(uuid())
  tenantId String @default(dbgenerated("(current_setting('app.tenant_id'::TEXT))::TEXT"))

  /// 防犯登録番号
  registrationNumber String
  /// 元のデータ
  raw                String

  // 共通
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}
