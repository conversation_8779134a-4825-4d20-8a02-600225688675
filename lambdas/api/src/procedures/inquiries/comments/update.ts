import { ImageInputSchema } from 'common';
import { z } from 'zod';
import { categorizeImageChanges } from '../../../image-funcs';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryCommentMutation = async ({
  input: { commentId, body, images },
  ctx: { prisma, asyncTx, userId, tenantId },
}: MutationArgs) => {
  const existingComment = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
    },
    include: {
      images: {
        where: { deleted: false },
      },
    },
  });

  if (!existingComment) {
    throw new Error('Comment not found');
  }

  if (existingComment.userId !== userId) {
    throw new Error('Permission denied: You can only edit your own comments');
  }

  await asyncTx(async (tx) => {
    // Update the comment body with explicit updatedAt timestamp
    await tx.inquiryComment.update({
      where: { id: commentId },
      data: {
        body,
        updatedAt: new Date(), // Explicitly set updatedAt to indicate user edit
      },
    });

    // SELECTIVE IMAGE UPDATE APPROACH using reusable utility
    // Only modify images that actually changed, preserving existing IDs and relationships

    const imageChanges = categorizeImageChanges(existingComment.images, images);

    // Execute only necessary operations within transaction

    // 1. Soft delete removed images
    if (imageChanges.toDelete.length > 0) {
      await tx.inquiryCommentImage.updateMany({
        where: {
          id: { in: imageChanges.toDelete.map((img) => img.id) },
        },
        data: {
          deleted: true,
          updatedAt: new Date(),
        },
      });
    }

    // 2. Update changed image metadata (preserves existing IDs and createdAt)
    for (const { existing, new: newImage } of imageChanges.toUpdate) {
      await tx.inquiryCommentImage.update({
        where: { id: existing.id },
        data: {
          description: newImage.description,
          sortOrder: newImage.sortOrder,
          updatedAt: new Date(),
        },
      });
    }

    // 3. Create new images
    if (imageChanges.toCreate.length > 0) {
      await tx.inquiryCommentImage.createMany({
        data: imageChanges.toCreate.map((image) => ({
          commentId,
          key: image.key,
          description: image.description,
          sortOrder: image.sortOrder,
        })),
      });
    }
  });

  return 'OK';
};

export const updateInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateInquiryCommentMutation);
