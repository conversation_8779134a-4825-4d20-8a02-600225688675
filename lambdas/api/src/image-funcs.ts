import type { ImageInputSchema } from 'common';
import type { z } from 'zod';

/**
 * Type representing an image input from the frontend
 */
export type ImageInput = z.infer<typeof ImageInputSchema>;

/**
 * Base interface that all image entities must implement for comparison
 */
export interface ImageEntity {
  id: string;
  key: string;
  description: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  deleted: boolean;
}

/**
 * Configuration options for image comparison
 */
export interface ImageComparisonOptions<T extends ImageEntity> {
  /** Field to use as unique identifier for matching images (typically 'key') */
  keyField: keyof T;
  /** Fields to compare for detecting metadata changes */
  compareFields: (keyof T)[];
}

/**
 * Result of categorizing image changes
 */
export interface ImageChangeResult<T extends ImageEntity> {
  /** Images that exist in database but not in new input (should be soft-deleted) */
  toDelete: T[];
  /** Images that exist in new input but not in database (should be created) */
  toCreate: ImageInput[];
  /** Images that exist in both but have different metadata (should be updated) */
  toUpdate: Array<{ existing: T; new: ImageInput }>;
}

/**
 * Categorizes image changes by comparing existing database images with new input images.
 * This utility function implements selective update logic that only modifies what actually changed,
 * avoiding the inefficient "delete all, then recreate" approach.
 *
 * @template T - The image entity type (e.g., InquiryCommentImage, InquiryImage)
 *
 * @param existingImages - Current images from the database
 * @param newImages - New images from the frontend form
 * @param options - Configuration for comparison
 * @param options.keyField - Field to use for matching (typically 'key')
 * @param options.compareFields - Fields to check for changes (e.g., ['description', 'sortOrder'])
 *
 * @returns Categorized arrays of images to delete, create, and update
 *
 * @example
 * ```typescript
 * // For InquiryCommentImage updates
 * const result = categorizeImageChanges(
 *   existingComment.images,
 *   formData.images,
 *   {
 *     keyField: 'key',
 *     compareFields: ['description', 'sortOrder']
 *   }
 * );
 *
 * // Execute only necessary database operations
 * if (result.toDelete.length > 0) {
 *   await softDeleteImages(result.toDelete);
 * }
 * if (result.toUpdate.length > 0) {
 *   await updateImageMetadata(result.toUpdate);
 * }
 * if (result.toCreate.length > 0) {
 *   await createNewImages(result.toCreate);
 * }
 * ```
 *
 * @example
 * ```typescript
 * // For different image entity types
 * const bicycleImageResult = categorizeImageChanges(
 *   existingBicycle.images,
 *   formData.images,
 *   {
 *     keyField: 'key',
 *     compareFields: ['description', 'sortOrder', 'category']
 *   }
 * );
 * ```
 *
 * **Performance Benefits:**
 * - No changes: 0 database operations (vs 2N with delete-all approach)
 * - Add 1 image: 1 operation (vs N+1 operations)
 * - Remove 1 image: 1 operation (vs 2N operations)
 * - Update metadata: 1 operation (vs 2N operations)
 *
 * **Data Integrity:**
 * - Preserves existing image IDs when unchanged
 * - Maintains original createdAt timestamps
 * - Only updates updatedAt when actual changes occur
 * - Supports soft-delete pattern for audit trail
 */
export function categorizeImageChanges<T extends ImageEntity>(
  existingImages: T[],
  newImages: ImageInput[],
  options: ImageComparisonOptions<T>,
): ImageChangeResult<T> {
  const { keyField, compareFields } = options;

  // Create lookup maps for efficient comparison using the specified key field
  const existingImageMap = new Map<string, T>();
  for (const img of existingImages) {
    const keyValue = String(img[keyField]);
    existingImageMap.set(keyValue, img);
  }

  const newImageMap = new Map<string, ImageInput>();
  for (const img of newImages) {
    const keyValue = String(img[keyField as keyof ImageInput]);
    newImageMap.set(keyValue, img);
  }

  // Categorize changes based on comparison
  const toDelete: T[] = [];
  const toCreate: ImageInput[] = [];
  const toUpdate: Array<{ existing: T; new: ImageInput }> = [];

  // Find images to delete (exist in database but not in new input)
  for (const existing of existingImages) {
    const keyValue = String(existing[keyField]);
    if (!newImageMap.has(keyValue)) {
      toDelete.push(existing);
    }
  }

  // Find images to create and update
  for (const newImage of newImages) {
    const keyValue = String(newImage[keyField as keyof ImageInput]);
    const existing = existingImageMap.get(keyValue);

    if (!existing) {
      // Image doesn't exist in database - create it
      toCreate.push(newImage);
    } else {
      // Image exists - check if metadata changed
      let hasChanges = false;

      for (const field of compareFields) {
        const existingValue = existing[field];
        const newValue = newImage[field as keyof ImageInput];

        if (existingValue !== newValue) {
          hasChanges = true;
          break;
        }
      }

      if (hasChanges) {
        toUpdate.push({ existing, new: newImage });
      }
      // If no changes, do nothing (preserve existing record)
    }
  }

  return {
    toDelete,
    toCreate,
    toUpdate,
  };
}

/**
 * Type guard to check if an object implements the ImageEntity interface
 */
export function isImageEntity(obj: unknown): obj is ImageEntity {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'key' in obj &&
    'description' in obj &&
    'sortOrder' in obj &&
    'createdAt' in obj &&
    'updatedAt' in obj &&
    'deleted' in obj
  );
}

/**
 * Helper function to validate that an array contains valid image entities
 */
export function validateImageEntities<T extends ImageEntity>(images: unknown[]): images is T[] {
  return images.every(isImageEntity);
}
