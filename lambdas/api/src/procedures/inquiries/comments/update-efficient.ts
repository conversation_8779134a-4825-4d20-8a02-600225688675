import { ImageInputSchema } from 'common';
import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryCommentMutationEfficient = async ({
  input: { commentId, body, images },
  ctx: { prisma, asyncTx, userId, tenantId },
}: MutationArgs) => {
  // First, verify the comment exists and user has permission to edit it
  const existingComment = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
    },
    include: {
      images: {
        where: { deleted: false },
      },
    },
  });

  if (!existingComment) {
    throw new Error('Comment not found');
  }

  if (existingComment.userId !== userId) {
    throw new Error('Permission denied: You can only edit your own comments');
  }

  // Use transaction to ensure atomicity
  await asyncTx(async (tx) => {
    // Update the comment body
    await tx.inquiryComment.update({
      where: { id: commentId },
      data: { 
        body,
        updatedAt: new Date(), // Explicitly set updatedAt to indicate user edit
      },
    });

    // EFFICIENT APPROACH: Only modify what actually changed
    
    // Get existing images for comparison
    const existingImages = existingComment.images;
    
    // Create maps for efficient lookup
    const existingImageMap = new Map(existingImages.map(img => [img.key, img]));
    const newImageMap = new Map(images.map(img => [img.key, img]));

    // Find images to delete (exist in DB but not in new list)
    const imagesToDelete = existingImages.filter(img => !newImageMap.has(img.key));
    
    // Find images to create (exist in new list but not in DB)
    const imagesToCreate = images.filter(img => !existingImageMap.has(img.key));
    
    // Find images to update (exist in both but might have different description/sortOrder)
    const imagesToUpdate = images.filter(img => {
      const existing = existingImageMap.get(img.key);
      return existing && (
        existing.description !== img.description || 
        existing.sortOrder !== img.sortOrder
      );
    });

    // Execute deletions (soft delete)
    if (imagesToDelete.length > 0) {
      await tx.inquiryCommentImage.updateMany({
        where: {
          id: { in: imagesToDelete.map(img => img.id) },
        },
        data: { deleted: true },
      });
    }

    // Execute creations
    if (imagesToCreate.length > 0) {
      await tx.inquiryCommentImage.createMany({
        data: imagesToCreate.map((image) => ({
          commentId,
          key: image.key,
          description: image.description,
          sortOrder: image.sortOrder,
        })),
      });
    }

    // Execute updates
    for (const image of imagesToUpdate) {
      const existing = existingImageMap.get(image.key)!;
      await tx.inquiryCommentImage.update({
        where: { id: existing.id },
        data: {
          description: image.description,
          sortOrder: image.sortOrder,
        },
      });
    }
  });

  return 'OK';
};

export const updateInquiryCommentEfficient = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateInquiryCommentMutationEfficient);
