import type { Context, TRPCProcedure } from '../../types';

type QueryArgs = { ctx: Context };

export const listInquiryQuery = async ({ ctx: { prisma, tenantId } }: QueryArgs) =>
  prisma.inquiry.findMany({
    where: { tenantId, originalId: null },
    include: {
      images: { orderBy: { sortOrder: 'asc' } },
      assignUsers: { include: { user: true } },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

export const listInquiry = (p: TRPCProcedure) => p.query(listInquiryQuery);
