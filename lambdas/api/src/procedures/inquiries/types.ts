import { ImageInputSchema } from 'common';
import { z } from 'zod';

const AssignmentChangeSchema = z.object({
  type: z.enum(['assign', 'unassign', 'reassign', 'selfAssign', 'noChange']),
  currentAssigneeId: z.string().optional(),
  newAssigneeId: z.string().optional(),
  shouldNotifyCurrentAssignee: z.boolean(),
  shouldNotifyNewAssignee: z.boolean(),
});

export const InquiryInputSchema = z
  .object({
    id: z.string().uuid(),
    receptionRouteId: z.string(),
    receiverId: z.string().nullable(),
    receivedAt: z.date().nullable(),
    title: z.string(),
    description: z.string(),
    address: z.string(),
    memo: z.string().nullable(),
    images: z.array(ImageInputSchema),
    assignUserId: z.string().optional(),
    assignmentChange: AssignmentChangeSchema.optional(),
  })
  .strict();
