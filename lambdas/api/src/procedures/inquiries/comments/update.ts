import { ImageInputSchema } from 'common';
import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryCommentMutation = async ({
  input: { commentId, body, images },
  ctx: { prisma, asyncTx, userId, tenantId },
}: MutationArgs) => {
  const existingComment = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
    },
    include: {
      images: {
        where: { deleted: false },
      },
    },
  });

  if (!existingComment) {
    throw new Error('Comment not found');
  }

  if (existingComment.userId !== userId) {
    throw new Error('Permission denied: You can only edit your own comments');
  }

  await asyncTx(async (tx) => {
    await tx.inquiryComment.update({
      where: { id: commentId },
      data: {
        body,
        // updatedAt: new Date(),
      },
    });

    await tx.inquiryCommentImage.updateMany({
      where: {
        commentId,
        deleted: false,
      },
      data: { deleted: true }, // ??
    });

    if (images.length > 0) {
      await tx.inquiryCommentImage.createMany({
        data: images.map((image) => ({
          commentId,
          key: image.key,
          description: image.description,
          sortOrder: image.sortOrder,
        })),
      });
    }
  });

  return 'OK';
};

export const updateInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateInquiryCommentMutation);
