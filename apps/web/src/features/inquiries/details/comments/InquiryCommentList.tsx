import { ImageAvatar } from '@/components/ImageAvatar';
import { Thumbnails } from '@/components/images/Thumbnails';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import { Comment, Delete, Edit, MoreVert } from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import type { InquiryComments } from 'lambda-api';
import { useState } from 'react';

type Props = {
  comments: InquiryComments;
  onEditComment?: (comment: InquiryComments[number]) => void;
  onDeleteComment?: (comment: InquiryComments[number]) => void;
};

const NoComments = () => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" gap={1} color="text.secondary">
        <Comment />
        <Typography variant="body2">まだコメントはありません</Typography>
      </Box>
    </CardContent>
  </Card>
);

const CommentActionMenu = ({
  onEdit,
  onDelete,
}: {
  onEdit: () => void;
  onDelete: () => void;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit();
    handleClose();
  };

  const handleDelete = () => {
    onDelete();
    handleClose();
  };

  return (
    <>
      <IconButton size="small" onClick={handleClick}>
        <MoreVert />
      </IconButton>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem onClick={handleEdit}>
          <Edit sx={{ mr: 1 }} />
          編集
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <Delete sx={{ mr: 1 }} />
          削除
        </MenuItem>
      </Menu>
    </>
  );
};

export default function InquiryCommentList({ comments, onEditComment, onDeleteComment }: Props) {
  console.log(
    '%c 🛣️: InquiryCommentList -> comments ',
    'font-size:16px;background-color:#aa335c;color:white;',
    comments,
  );
  const { user } = useAppContext();

  if (!comments || comments.length === 0) {
    return <NoComments />;
  }

  return (
    <Stack spacing={2}>
      <Typography variant="h6" component="h3">
        コメント ({comments.length})
      </Typography>
      {comments.map((comment) => {
        const isOwnComment = comment.userId === user.id;
        const isEdited = comment.updatedAt.getTime() !== comment.createdAt.getTime();
        console.log(`🔨 comment name: ${comment.body}, isEdited flag is: ${isEdited} `);

        return (
          <Card key={comment.id} variant="outlined">
            <CardHeader
              avatar={<ImageAvatar alt={comment.user.name} />}
              title={<Typography variant="subtitle2">{comment.user.name}</Typography>}
              subheader={
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    {datetimeFormatter(new Date(comment.createdAt))}
                  </Typography>
                  {isEdited && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      (編集済み)
                    </Typography>
                  )}
                </Box>
              }
              action={
                isOwnComment &&
                onEditComment &&
                onDeleteComment && (
                  <CommentActionMenu
                    onEdit={() => onEditComment(comment)}
                    onDelete={() => onDeleteComment(comment)}
                  />
                )
              }
            />
            <CardContent sx={{ pt: 0 }}>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {comment.body}
              </Typography>
              {comment.images && comment.images.length > 0 && (
                <Box mt={2}>
                  <Thumbnails images={comment.images} size="small" minCols={3} />
                </Box>
              )}
            </CardContent>
          </Card>
        );
      })}
    </Stack>
  );
}
