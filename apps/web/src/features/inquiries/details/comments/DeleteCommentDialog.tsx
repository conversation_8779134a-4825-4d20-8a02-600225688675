import { trpc } from '@/api';
import { Delete } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Alert,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { InquiryComments } from 'lambda-api';

type Props = {
  open: boolean;
  onClose: () => void;
  comment: InquiryComments[number];
  inquiryId: string;
};

export default function DeleteCommentDialog({ open, onClose, comment, inquiryId }: Props) {
  const queryClient = useQueryClient();

  const { mutateAsync, error, isPending } = trpc.inquiries.comments.delete.useMutation({
    onSuccess: () => {
      const queryKey = getQueryKey(trpc.inquiries.comments.list, { inquiryId });
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
  });

  const handleDelete = async () => {
    await mutateAsync({ commentId: comment.id });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>コメントを削除</DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            コメントの削除に失敗しました
          </Alert>
        )}

        <DialogContentText>
          このコメントを削除してもよろしいですか？この操作は取り消すことができません。
        </DialogContentText>
      </DialogContent>

      <DialogActions>
        <Box display="flex" gap={1}>
          <Button variant="outlined" onClick={onClose} disabled={isPending}>
            キャンセル
          </Button>
          <LoadingButton
            variant="contained"
            color="error"
            startIcon={<Delete />}
            loading={isPending}
            onClick={handleDelete}
          >
            削除する
          </LoadingButton>
        </Box>
      </DialogActions>
    </Dialog>
  );
}
