import type { ImageInputSchema } from 'common';
import type { z } from 'zod';

/**
 * Type representing an image input from the frontend
 */
export type ImageInput = z.infer<typeof ImageInputSchema>;

/**
 * Base interface that all image entities must implement for comparison
 */
export interface ImageEntity {
  id: string;
  key: string;
  description: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
  deleted: boolean;
}

/**
 * Result of categorizing image changes
 */
export interface ImageChangeResult<T extends ImageEntity> {
  /** Images that exist in database but not in new input (should be soft-deleted) */
  toDelete: T[];
  /** Images that exist in new input but not in database (should be created) */
  toCreate: ImageInput[];
  /** Images that exist in both but have different metadata (should be updated) */
  toUpdate: Array<{ existing: T; new: ImageInput }>;
}

/**
 * Categorizes image changes by comparing existing database images with new input images.
 * This utility function implements selective update logic that only modifies what actually changed,
 * avoiding the inefficient "delete all, then recreate" approach.
 *
 * Uses standardized comparison logic:
 * - Matching field: 'key' (unique identifier)
 * - Comparison fields: 'description', 'sortOrder' (metadata that can change)
 *
 * @template T - The image entity type (e.g., InquiryCommentImage, InquiryImage)
 *
 * @param existingImages - Current images from the database
 * @param newImages - New images from the frontend form
 *
 * @returns Categorized arrays of images to delete, create, and update
 *
 * @example
 * ```typescript
 * // For InquiryCommentImage updates
 * const result = categorizeImageChanges(
 *   existingComment.images,
 *   formData.images
 * );
 *
 * // Execute only necessary database operations
 * if (result.toDelete.length > 0) {
 *   await softDeleteImages(result.toDelete);
 * }
 * if (result.toUpdate.length > 0) {
 *   await updateImageMetadata(result.toUpdate);
 * }
 * if (result.toCreate.length > 0) {
 *   await createNewImages(result.toCreate);
 * }
 * ```
 *
 * @example
 * ```typescript
 * // For any image entity type
 * const bicycleImageResult = categorizeImageChanges(
 *   existingBicycle.images,
 *   formData.images
 * );
 * ```
 *
 * **Performance Benefits:**
 * - No changes: 0 database operations (vs 2N with delete-all approach)
 * - Add 1 image: 1 operation (vs N+1 operations)
 * - Remove 1 image: 1 operation (vs 2N operations)
 * - Update metadata: 1 operation (vs 2N operations)
 *
 * **Data Integrity:**
 * - Preserves existing image IDs when unchanged
 * - Maintains original createdAt timestamps
 * - Only updates updatedAt when actual changes occur
 * - Supports soft-delete pattern for audit trail
 */
export function categorizeImageChanges<T extends ImageEntity>(
  existingImages: T[],
  newImages: ImageInput[],
): ImageChangeResult<T> {
  // Create lookup maps for efficient comparison using the key field
  const existingImageMap = new Map<string, T>();
  for (const img of existingImages) {
    existingImageMap.set(img.key, img);
  }

  const newImageMap = new Map<string, ImageInput>();
  for (const img of newImages) {
    newImageMap.set(img.key, img);
  }

  // Categorize changes based on comparison
  const toDelete: T[] = [];
  const toCreate: ImageInput[] = [];
  const toUpdate: Array<{ existing: T; new: ImageInput }> = [];

  // Find images to delete (exist in database but not in new input)
  for (const existing of existingImages) {
    if (!newImageMap.has(existing.key)) {
      toDelete.push(existing);
    }
  }

  // Find images to create and update
  for (const newImage of newImages) {
    const existing = existingImageMap.get(newImage.key);

    if (!existing) {
      // Image doesn't exist in database - create it
      toCreate.push(newImage);
    } else {
      // Image exists - check if description or sortOrder changed
      const hasChanges =
        existing.description !== newImage.description || existing.sortOrder !== newImage.sortOrder;

      if (hasChanges) {
        toUpdate.push({ existing, new: newImage });
      }
      // If no changes, do nothing (preserve existing record)
    }
  }

  return {
    toDelete,
    toCreate,
    toUpdate,
  };
}

// /**
//  * Type guard to check if an object implements the ImageEntity interface
//  */
// export function isImageEntity(obj: unknown): obj is ImageEntity {
//   return (
//     typeof obj === 'object' &&
//     obj !== null &&
//     'id' in obj &&
//     'key' in obj &&
//     'description' in obj &&
//     'sortOrder' in obj &&
//     'createdAt' in obj &&
//     'updatedAt' in obj &&
//     'deleted' in obj
//   );
// }

// /**
//  * Helper function to validate that an array contains valid image entities
//  */
// export function validateImageEntities<T extends ImageEntity>(images: unknown[]): images is T[] {
//   return images.every(isImageEntity);
// }
