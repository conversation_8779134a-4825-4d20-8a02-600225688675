import { z } from 'zod';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const MigrationScalarFieldEnumSchema = z.enum(['id','success','sql','log','createdAt','updatedAt']);

export const RelationLoadStrategySchema = z.enum(['query','join']);

export const MonthlyAnnouncementListScalarFieldEnumSchema = z.enum(['id','tenantId','year','month','startedAt','days','memo','createdAt','updatedAt','deleted']);

export const BicycleColorScalarFieldEnumSchema = z.enum(['id','tenantId','name','nameUnique','code','sortOrder','createdAt','updatedAt','deleted']);

export const ColorsOnBodiesScalarFieldEnumSchema = z.enum(['tenantId','colorId','bodyId','createdAt','updatedAt']);

export const BicycleConditionScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','createdAt','updatedAt','deleted']);

export const ConditionsOnBodiesScalarFieldEnumSchema = z.enum(['tenantId','conditionId','bodyId','createdAt','updatedAt']);

export const BicycleMakerScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleNameScalarFieldEnumSchema = z.enum(['id','tenantId','makerId','code','name','createdAt','updatedAt','deleted']);

export const BicycleStyleScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleStyleImageScalarFieldEnumSchema = z.enum(['id','tenantId','styleId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleReadLogScalarFieldEnumSchema = z.enum(['id','tenantId','userId','bicycleId','createdAt']);

export const BicycleEventLogScalarFieldEnumSchema = z.enum(['id','tenantId','userId','bicycleId','eventId','createdAt']);

export const BicycleMarkerScalarFieldEnumSchema = z.enum(['id','tenantId','userId','landmarkId','teamId','status','isNoParkingArea','lat','lng','accuracy','prefecture','city','town','furtherAddress','memo','createdAt','updatedAt','deleted']);

export const BicycleMarkerImageScalarFieldEnumSchema = z.enum(['id','tenantId','markerId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleScalarFieldEnumSchema = z.enum(['id','tenantId','status','type','available','locationId','bodyId','serialTagId','theftReportId','ownerId','notificationId','announcementId','deadlineId','assessmentId','storageLocationId','teamRefId','createdAt','updatedAt','deleted']);

export const BicycleEventScalarFieldEnumSchema = z.enum(['id','tenantId','bicycleId','userId','locationId','bodyId','serialTagId','theftReportId','ownerId','notificationId','announcementId','deadlineId','assessmentId','storageLocationId','teamRefId','destRegistrationNumberId','monthlyAnnouncementListId','sellContractId','transferContractId','disposeContractId','noPaymentReasonId','type','date','memo','cancelId','createdAt','updatedAt','deleted']);

export const BicycleStoragePeriodScalarFieldEnumSchema = z.enum(['id','tenantId','bicycleId','storageId','from','to','createdAt','updatedAt','deleted']);

export const BicycleImageScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleLocationMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleLocationScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','landmarkId','isNoParkingArea','lat','lng','accuracy','prefecture','city','town','furtherAddress','createdAt','updatedAt','deleted']);

export const BicycleBodyMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleBodyScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','styleId','makerId','bicycleNameId','type','available','hasBasket','isLocked','free1','free2','registrationNumber','registrationNumberPrefectureCode','registrationNumberPoliceCode','registrationNumber1','registrationNumber2','serialNumber','numberPlate','numberPlateTypeCode','numberPlateDltbName','numberPlateObjectiveCode','numberPlateNumberSection','createdAt','updatedAt','deleted']);

export const BicycleNotificationMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleNotificationScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','date','createdAt','updatedAt','deleted']);

export const BicycleAnnouncementMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleAnnouncementScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','start','end','createdAt','updatedAt','deleted']);

export const BicycleDeadlineMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleDeadlineScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','date','createdAt','updatedAt','deleted']);

export const BicycleAssessmentMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const PriceSuggestionScalarFieldEnumSchema = z.enum(['id','tenantId','price','description','sortOrder','createdAt','updatedAt','deleted']);

export const BicycleAssessmentScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','price','createdAt','updatedAt','deleted']);

export const TheftReportMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const TheftReportScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','reported','reportedAt','createdAt','updatedAt','deleted']);

export const BicycleSerialTagMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleSerialTagScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','storageId','landmarkId','originalId','partitionKey','serialNo','seq','seqUnique','fiscalYear','year','month','day','type','createdAt','updatedAt','deleted']);

export const BicycleOwnerMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','activeId','createdAt','updatedAt','deleted']);

export const BicycleOwnerScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','sourceType','name','postalCode','address','tel','createdAt','updatedAt','deleted']);

export const BicycleStorageLocationMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const BicycleStorageLocationScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','storageId','memo','createdAt','updatedAt','deleted']);

export const TeamRefMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const TeamRefScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','refId','createdAt','updatedAt','deleted']);

export const StorageFeePaymentScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','fee','createdAt','updatedAt','deleted']);

export const BicycleRegistrationNumberScalarFieldEnumSchema = z.enum(['id','tenantId','registrationNumber','raw','createdAt','updatedAt','deleted']);

export const BicycleSellContractScalarFieldEnumSchema = z.enum(['id','tenantId','dealerId','status','price','date','memo','createdAt','updatedAt','deleted']);

export const BicycleTransferContractScalarFieldEnumSchema = z.enum(['id','tenantId','dealerId','status','date','createdAt','updatedAt','deleted']);

export const BicycleDisposeContractScalarFieldEnumSchema = z.enum(['id','tenantId','dealerId','status','cost','date','createdAt','updatedAt','deleted']);

export const BicycleDealerScalarFieldEnumSchema = z.enum(['id','tenantId','originalId','dealTypes','name','nameUnique','address','email','tel','memo','createdAt','updatedAt','deleted']);

export const DltbContactScalarFieldEnumSchema = z.enum(['id','tenantId','numberPlateLocationId','postalCode','address','organization','department','person','tel','fax','sortOrder','createdAt','updatedAt','deleted']);

export const HolidayScalarFieldEnumSchema = z.enum(['id','tenantId','date','name','memo','createdAt','updatedAt','deleted']);

export const InquiryScalarFieldEnumSchema = z.enum(['id','tenantId','originalId','status','urgencyId','bicycleId','receptionRouteId','receiverId','fiscalYear','no','receivedAt','title','description','address','lat','lng','memo','createdAt','updatedAt','deleted']);

export const InquiryImageScalarFieldEnumSchema = z.enum(['id','tenantId','inquiryId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const InquiryCommentScalarFieldEnumSchema = z.enum(['id','tenantId','inquiryId','userId','body','createdAt','updatedAt','deleted']);

export const InquiryCommentImageScalarFieldEnumSchema = z.enum(['id','tenantId','commentId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const ReceptionRouteScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','memo','createdAt','updatedAt','deleted']);

export const InquiriesOnUsersScalarFieldEnumSchema = z.enum(['tenantId','inquiryId','userId','createdAt','updatedAt']);

export const InquiriesOnTeamsScalarFieldEnumSchema = z.enum(['tenantId','inquiryId','teamId','createdAt','updatedAt']);

export const InquiryUrgencyScalarFieldEnumSchema = z.enum(['id','tenantId','name','colorCode','sortOrder','memo','createdAt','updatedAt','deleted']);

export const LandmarkScalarFieldEnumSchema = z.enum(['id','tenantId','name','code','sortOrder','address','lat','lng','createdAt','updatedAt','deleted']);

export const LandmarkImageScalarFieldEnumSchema = z.enum(['id','tenantId','landmarkId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const RemoveRuleScalarFieldEnumSchema = z.enum(['id','tenantId','bicycleTypes','storageId','landmarkId','sortOrder','createdAt','updatedAt']);

export const MotorizedBicycleNumberPlateContactScalarFieldEnumSchema = z.enum(['id','tenantId','numberPlateLocationId','postalCode','address','organization','department','person','tel','fax','sortOrder','createdAt','updatedAt','deleted']);

export const NumberPlateLocationScalarFieldEnumSchema = z.enum(['id','tenantId','name','prefecture','dltb','dltbBranch','areas','createdAt','updatedAt','deleted']);

export const ParkingScalarFieldEnumSchema = z.enum(['id','tenantId','landmarkId','name','code','sortOrder','address','lat','lng','memo','createdAt','updatedAt','deleted']);

export const PoliceStationScalarFieldEnumSchema = z.enum(['id','tenantId','name','code','prefecture','prefectureRaw','prefectureCode','createdAt','updatedAt','deleted']);

export const PostalDataScalarFieldEnumSchema = z.enum(['code','tenantId','index','owner','prefecture','prefectureKana','city','cityKana','townArea','townAreaRaw','townAreaKana','hasChome','chomeList','nationalLocalPublicEntityCode','oldPostalCode','hasAnotherPostalCode','isNumberedForEachKoaza','hasMultipleTownArea','createdAt','updatedAt','deleted']);

export const PoliceReferenceScalarFieldEnumSchema = z.enum(['id','tenantId','prefecture','prefectureCode','status','requestedAt','respondedAt','memo','createdAt','updatedAt','deleted']);

export const PoliceRequestScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','referenceId','no','serialNo','registrationNumber','serialNumber','createdAt','updatedAt','deleted']);

export const PoliceResponseScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','referenceId','requestId','status','name','postalCode','address','theftReport','theftReportedAt','receiptNumber','createdAt','updatedAt','deleted']);

export const DltbReferenceScalarFieldEnumSchema = z.enum(['id','tenantId','status','requestedAt','respondedAt','memo','createdAt','updatedAt','deleted']);

export const DltbRequestScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','referenceId','no','serialNo','description','createdAt','updatedAt','deleted']);

export const DltbResponseScalarFieldEnumSchema = z.enum(['id','tenantId','eventId','referenceId','requestId','status','name','postalCode','address','receiptNumber','createdAt','updatedAt','deleted']);

export const RegistrationNumberContactScalarFieldEnumSchema = z.enum(['id','tenantId','policeStationId','postalCode','address','organization','department','person','tel','fax','sortOrder','createdAt','updatedAt','deleted']);

export const RegistrationNumberMetaScalarFieldEnumSchema = z.enum(['id','tenantId','lastId','createdAt','updatedAt','deleted']);

export const RegistrationNumberScalarFieldEnumSchema = z.enum(['id','tenantId','metaId','srcEventId','registrationNumber','sourceType','name','postalCode','address','tel','createdAt','updatedAt','deleted']);

export const ReleaseTagScalarFieldEnumSchema = z.enum(['id','tenantId','type','code','sortOrder','description','createdAt','updatedAt','deleted']);

export const StorageScalarFieldEnumSchema = z.enum(['id','tenantId','name','sectionName','managerName','code','sortOrder','address','lat','lng','createdAt','updatedAt','deleted']);

export const StorageImageScalarFieldEnumSchema = z.enum(['id','tenantId','storageId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const StorageMapImageScalarFieldEnumSchema = z.enum(['id','tenantId','storageId','key','description','createdAt','updatedAt','deleted']);

export const TenantScalarFieldEnumSchema = z.enum(['id','name','shortName','postalCode','address','subdomain','userPoolId','logoKey','primaryColor','secondaryColor','dateFormat','defaultPrefecture','defaultPoliceStationPrefectureCode','defaultPoliceStationId','createdAt','updatedAt','deleted']);

export const LabelScalarFieldEnumSchema = z.enum(['id','tenantId','group','key','value','visible','createdAt','updatedAt','deleted']);

export const ImageSettingsScalarFieldEnumSchema = z.enum(['id','tenantId','markId','findId','ensureAbandonedId','removeId','storeId','returnToOwnerId','min','max','createdAt','updatedAt','deleted']);

export const ImageGuideScalarFieldEnumSchema = z.enum(['id','tenantId','imageSettingsId','key','description','sortOrder','createdAt','updatedAt','deleted']);

export const MapSettingsScalarFieldEnumSchema = z.enum(['tenantId','defaultLatitude','defaultLongitude','apiKey','mapId','createdAt','updatedAt','deleted']);

export const PatrolSettingsScalarFieldEnumSchema = z.enum(['tenantId','flow','createdAt','updatedAt','deleted']);

export const MarkSettingsScalarFieldEnumSchema = z.enum(['tenantId','createdAt','updatedAt','deleted']);

export const FindSettingsScalarFieldEnumSchema = z.enum(['tenantId','requiredBicycleType','additionalBodyFields','createdAt','updatedAt','deleted']);

export const EnsureAbandonedSettingsScalarFieldEnumSchema = z.enum(['tenantId','allow','requiredBicycleType','additionalBodyFields','createdAt','updatedAt','deleted','imageSettingsId']);

export const RemoveSettingsScalarFieldEnumSchema = z.enum(['tenantId','minutes','requiredBicycleType','additionalBodyFields','createdAt','updatedAt','deleted']);

export const NumberingSettingsScalarFieldEnumSchema = z.enum(['tenantId','serialTagPaper','serialNoPartitions','serialNoFormat','createdAt','updatedAt','deleted']);

export const StoreSettingsScalarFieldEnumSchema = z.enum(['tenantId','allowEntryBodyInfoLater','free1','free2','createdAt','updatedAt','deleted']);

export const ReturnToOwnerSettingsScalarFieldEnumSchema = z.enum(['tenantId','allowPrepay','createdAt','updatedAt','deleted']);

export const PoliceReferenceSettingsScalarFieldEnumSchema = z.enum(['tenantId','fileType','csvIgnoreIndexes','csvIgnoreLastIndexes','xlsxIgnoreIndexes','xlsxIgnoreLastIndexes','createdAt','updatedAt','deleted']);

export const NotificationSettingsScalarFieldEnumSchema = z.enum(['tenantId','deadlineDays','createdAt','updatedAt','deleted']);

export const AnnouncementSettingsScalarFieldEnumSchema = z.enum(['tenantId','flow','days','createdAt','updatedAt','deleted']);

export const DeadlineSettingsScalarFieldEnumSchema = z.enum(['tenantId','days','createdAt','updatedAt','deleted']);

export const RecycleSettingsScalarFieldEnumSchema = z.enum(['tenantId','days','createdAt','updatedAt','deleted']);

export const BicycleTypeSettingScalarFieldEnumSchema = z.enum(['id','tenantId','type','name','code','storageFee','sortOrder','createdAt','updatedAt','deleted']);

export const ReasonForNoStorageFeePaymentScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','createdAt','updatedAt','deleted']);

export const UserScalarFieldEnumSchema = z.enum(['id','tenantId','roleId','name','displayName','displayNameUnique','kana','email','emailUnique','tel','memo','canLogin','createdAt','updatedAt','deleted']);

export const RoleScalarFieldEnumSchema = z.enum(['id','tenantId','name','description','sortOrder','features','createdAt','updatedAt','deleted']);

export const SearchSetScalarFieldEnumSchema = z.enum(['id','tenantId','name','sortOrder','createdAt','updatedAt']);

export const SearchItemScalarFieldEnumSchema = z.enum(['id','tenantId','setId','field','type','sortOrder','createdAt','updatedAt']);

export const SearchSetsOnRolesScalarFieldEnumSchema = z.enum(['tenantId','setId','roleId','sortOrder','updatedAt','createdAt']);

export const NotificationScalarFieldEnumSchema = z.enum(['id','tenantId','roleId','teamId','inquiryId','title','description','type','scope','createdAt','updatedAt','deleted']);

export const NotificationsOnUsersScalarFieldEnumSchema = z.enum(['tenantId','notificationId','userId','readAt','hide','createdAt','updatedAt']);

export const TeamScalarFieldEnumSchema = z.enum(['id','tenantId','name','description','logoKey','createdAt','updatedAt','deleted']);

export const TeamsOnUsersScalarFieldEnumSchema = z.enum(['tenantId','teamId','userId','createdAt','updatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const NullsOrderSchema = z.enum(['first','last']);

export const BicycleMarkerStatusSchema = z.enum(['todo','done']);

export type BicycleMarkerStatus = `${z.infer<typeof BicycleMarkerStatusSchema>}`

export const BicycleStatusSchema = z.enum(['find','ensureAbandoned','lose','ignore','remove','store','returnToOwner','sell','transfer','dispose','recycle','loseFromStorage']);

export type BicycleStatus = `${z.infer<typeof BicycleStatusSchema>}`

export const BicycleEventTypeSchema = z.enum(['find','ensureAbandoned','lose','ignore','remove','numbering','reNumbering','printSerialTag','store','assess','copyOwnerInfo','appendAnnouncementList','removeAnnouncementList','startAnnouncement','updateAnnouncement','cancelAnnouncement','requestPolice','receivePolice','cancelPolice','requestDltb','receiveDltb','cancelDltb','notify','updateNotification','cancelNotification','resetNotificationDate','extendDeadline','scheduleDeadline','updateDeadline','cancelDeadline','scheduleMoveBetweenStorage','moveBetweenStorage','returnToOwner','cancelReturnToOwner','collectStorageFee','cancelCollectStorageFee','sell','cancelSell','scheduleSell','editScheduleSell','cancelSellSchedule','transfer','cancelTransfer','scheduleTransfer','cancelTransferSchedule','dispose','cancelDispose','scheduleDispose','cancelDisposeSchedule','recycle','cancelRecycle','scheduleRecycle','cancelRecycleSchedule','loseFromStorage','cancelLoseFromStorage','create','editLocation','editBody','editOwner']);

export type BicycleEventType = `${z.infer<typeof BicycleEventTypeSchema>}`

export const BicycleTypeSchema = z.enum(['bicycle','motorizedBicycle','motorcycle','specifiedSmallMotorizedBicycle','other']);

export type BicycleType = `${z.infer<typeof BicycleTypeSchema>}`

export const BicycleContractStatusSchema = z.enum(['scheduled','done','canceled']);

export type BicycleContractStatus = `${z.infer<typeof BicycleContractStatusSchema>}`

export const DealTypeSchema = z.enum(['sell','transfer','dispose']);

export type DealType = `${z.infer<typeof DealTypeSchema>}`

export const InquiryStatusSchema = z.enum(['notRequiredAction','notStarted','inProgress','waitingForReview','completed']);

export type InquiryStatus = `${z.infer<typeof InquiryStatusSchema>}`

export const PostalDataOwnerSchema = z.enum(['system','user']);

export type PostalDataOwner = `${z.infer<typeof PostalDataOwnerSchema>}`

export const PrefectureSchema = z.enum(['北海道','青森県','岩手県','宮城県','秋田県','山形県','福島県','茨城県','栃木県','群馬県','埼玉県','千葉県','東京都','神奈川県','新潟県','富山県','石川県','福井県','山梨県','長野県','岐阜県','静岡県','愛知県','三重県','滋賀県','京都府','大阪府','兵庫県','奈良県','和歌山県','鳥取県','島根県','岡山県','広島県','山口県','徳島県','香川県','愛媛県','高知県','福岡県','佐賀県','長崎県','熊本県','大分県','宮崎県','鹿児島県','沖縄県']);

export type Prefecture = `${z.infer<typeof PrefectureSchema>}`

export const ReferenceStatusSchema = z.enum(['request','response','canceled']);

export type ReferenceStatus = `${z.infer<typeof ReferenceStatusSchema>}`

export const ResponseStatusSchema = z.enum(['success','notFound']);

export type ResponseStatus = `${z.infer<typeof ResponseStatusSchema>}`

export const SourceTypeSchema = z.enum(['body','policeReference','dltbReference','recipient','keepExtend','manual']);

export type SourceType = `${z.infer<typeof SourceTypeSchema>}`

export const ReleaseTypeSchema = z.enum(['dispose','recycle','sell','transfer']);

export type ReleaseType = `${z.infer<typeof ReleaseTypeSchema>}`

export const DateFormatPatternSchema = z.enum(['YYYY_MM_DD_SLASH','YY_MM_DD_SLASH','YYYY_MM_DD_HYPHEN','YY_MM_DD_HYPHEN']);

export type DateFormatPattern = `${z.infer<typeof DateFormatPatternSchema>}`

export const SerialNoPartitionSchema = z.enum(['year','month','day','type','storage','landmark']);

export type SerialNoPartition = `${z.infer<typeof SerialNoPartitionSchema>}`

export const PatrolFlowSchema = z.enum(['mark','find','noPatrol']);

export type PatrolFlow = `${z.infer<typeof PatrolFlowSchema>}`

export const PoliceReferenceFileTypeSchema = z.enum(['any','csv','xlsx']);

export type PoliceReferenceFileType = `${z.infer<typeof PoliceReferenceFileTypeSchema>}`

export const AnnouncementFlowSchema = z.enum(['each','monthly']);

export type AnnouncementFlow = `${z.infer<typeof AnnouncementFlowSchema>}`

export const BodyFieldSchema = z.enum(['colors','hasBasket','isLocked','conditions','style','free1','free2','registrationNumber','serialNumber','numberPlate']);

export type BodyField = `${z.infer<typeof BodyFieldSchema>}`

export const SerialTagPaperSchema = z.enum(['A4_vertical3x4','A4_vertical4x6']);

export type SerialTagPaper = `${z.infer<typeof SerialTagPaperSchema>}`

export const SearchFieldSchema = z.enum(['createdAt','removedAt','status','storage','storageLocationMemo','serialNo','bicycleType','registrationNumber','serialNumber','numberPlate','colors','hasBasket','isLocked','conditions','landmark','isNoParkingArea','address','isRemovedOrStored','referenceStatus','ownerName','ownerPostalCode','ownerAddress','theftReport','announceStatus','isReturned','isCollectedStorageFee','isReleased','releaseStatus','ownerStatus']);

export type SearchField = `${z.infer<typeof SearchFieldSchema>}`

export const SearchItemTypeSchema = z.enum(['text','switch','selectSingle','selectMultiple','date','dateAfter','dateBefore']);

export type SearchItemType = `${z.infer<typeof SearchItemTypeSchema>}`

export const NotificationTypeSchema = z.enum(['system','inquiryAssignment']);

export type NotificationType = `${z.infer<typeof NotificationTypeSchema>}`

export const NotificationScopeSchema = z.enum(['all','role','user','team']);

export type NotificationScope = `${z.infer<typeof NotificationScopeSchema>}`

